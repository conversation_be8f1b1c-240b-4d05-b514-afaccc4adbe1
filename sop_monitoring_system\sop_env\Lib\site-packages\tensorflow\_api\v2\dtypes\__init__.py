# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.dtypes namespace
"""

import sys as _sys

from tensorflow._api.v2.dtypes import experimental
from tensorflow.python.framework.dtypes import DType # line: 51
from tensorflow.python.framework.dtypes import QUANTIZED_DTYPES # line: 808
from tensorflow.python.framework.dtypes import as_dtype # line: 830
from tensorflow.python.framework.dtypes import bfloat16 # line: 420
from tensorflow.python.framework.dtypes import bool # line: 394
from tensorflow.python.framework.dtypes import complex128 # line: 382
from tensorflow.python.framework.dtypes import complex64 # line: 376
from tensorflow.python.framework.dtypes import double # line: 372
from tensorflow.python.framework.dtypes import float16 # line: 357
from tensorflow.python.framework.dtypes import float32 # line: 364
from tensorflow.python.framework.dtypes import float64 # line: 370
from tensorflow.python.framework.dtypes import half # line: 358
from tensorflow.python.framework.dtypes import int16 # line: 342
from tensorflow.python.framework.dtypes import int32 # line: 346
from tensorflow.python.framework.dtypes import int64 # line: 350
from tensorflow.python.framework.dtypes import int8 # line: 338
from tensorflow.python.framework.dtypes import qint16 # line: 402
from tensorflow.python.framework.dtypes import qint32 # line: 406
from tensorflow.python.framework.dtypes import qint8 # line: 398
from tensorflow.python.framework.dtypes import quint16 # line: 414
from tensorflow.python.framework.dtypes import quint8 # line: 410
from tensorflow.python.framework.dtypes import resource # line: 312
from tensorflow.python.framework.dtypes import string # line: 390
from tensorflow.python.framework.dtypes import uint16 # line: 326
from tensorflow.python.framework.dtypes import uint32 # line: 330
from tensorflow.python.framework.dtypes import uint64 # line: 334
from tensorflow.python.framework.dtypes import uint8 # line: 322
from tensorflow.python.framework.dtypes import variant # line: 318
from tensorflow.python.ops.math_ops import cast # line: 940
from tensorflow.python.ops.math_ops import complex # line: 695
from tensorflow.python.ops.math_ops import saturate_cast # line: 1025
