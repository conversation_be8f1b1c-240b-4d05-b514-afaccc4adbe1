# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.config namespace
"""

import sys as _sys

from tensorflow._api.v2.config import experimental
from tensorflow._api.v2.config import optimizer
from tensorflow._api.v2.config import threading
from tensorflow.python.eager.context import LogicalDevice # line: 391
from tensorflow.python.eager.context import LogicalDeviceConfiguration # line: 409
from tensorflow.python.eager.context import PhysicalDevice # line: 457
from tensorflow.python.eager.polymorphic_function.eager_function_run import experimental_functions_run_eagerly # line: 108
from tensorflow.python.eager.polymorphic_function.eager_function_run import experimental_run_functions_eagerly # line: 82
from tensorflow.python.eager.polymorphic_function.eager_function_run import functions_run_eagerly # line: 25
from tensorflow.python.eager.polymorphic_function.eager_function_run import run_functions_eagerly # line: 31
from tensorflow.python.eager.remote import connect_to_cluster as experimental_connect_to_cluster # line: 76
from tensorflow.python.eager.remote import connect_to_remote_host as experimental_connect_to_host # line: 37
from tensorflow.python.framework.config import get_logical_device_configuration # line: 802
from tensorflow.python.framework.config import get_soft_device_placement # line: 257
from tensorflow.python.framework.config import get_visible_devices # line: 500
from tensorflow.python.framework.config import list_logical_devices # line: 461
from tensorflow.python.framework.config import list_physical_devices # line: 424
from tensorflow.python.framework.config import set_logical_device_configuration # line: 843
from tensorflow.python.framework.config import set_soft_device_placement # line: 283
from tensorflow.python.framework.config import set_visible_devices # line: 533
