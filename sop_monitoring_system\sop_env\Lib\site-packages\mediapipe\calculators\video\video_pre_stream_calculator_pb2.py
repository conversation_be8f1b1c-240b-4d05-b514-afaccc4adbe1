# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/video/video_pre_stream_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=mediapipe/calculators/video/video_pre_stream_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xcf\x02\n\x1fVideoPreStreamCalculatorOptions\x12;\n\x03\x66ps\x18\x01 \x01(\x0b\x32..mediapipe.VideoPreStreamCalculatorOptions.Fps\x1a\x94\x01\n\x03\x46ps\x12\r\n\x05value\x18\x01 \x01(\x01\x12H\n\x05ratio\x18\x02 \x01(\x0b\x32\x39.mediapipe.VideoPreStreamCalculatorOptions.Fps.Rational32\x1a\x34\n\nRational32\x12\x11\n\tnumerator\x18\x01 \x01(\x05\x12\x13\n\x0b\x64\x65nominator\x18\x02 \x01(\x05\x32X\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x8b\xf0\x97H \x01(\x0b\x32*.mediapipe.VideoPreStreamCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.video.video_pre_stream_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_VIDEOPRESTREAMCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_VIDEOPRESTREAMCALCULATOROPTIONS']._serialized_start=115
  _globals['_VIDEOPRESTREAMCALCULATOROPTIONS']._serialized_end=450
  _globals['_VIDEOPRESTREAMCALCULATOROPTIONS_FPS']._serialized_start=212
  _globals['_VIDEOPRESTREAMCALCULATOROPTIONS_FPS']._serialized_end=360
  _globals['_VIDEOPRESTREAMCALCULATOROPTIONS_FPS_RATIONAL32']._serialized_start=308
  _globals['_VIDEOPRESTREAMCALCULATOROPTIONS_FPS_RATIONAL32']._serialized_end=360
# @@protoc_insertion_point(module_scope)
