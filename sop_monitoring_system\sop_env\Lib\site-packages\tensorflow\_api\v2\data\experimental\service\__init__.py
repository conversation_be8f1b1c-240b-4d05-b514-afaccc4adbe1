# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.data.experimental.service namespace
"""

import sys as _sys

from tensorflow.python.data.experimental.ops.data_service_ops import CrossTrainerCache # line: 119
from tensorflow.python.data.experimental.ops.data_service_ops import ShardingPolicy # line: 48
from tensorflow.python.data.experimental.ops.data_service_ops import distribute # line: 543
from tensorflow.python.data.experimental.ops.data_service_ops import from_dataset_id # line: 1067
from tensorflow.python.data.experimental.ops.data_service_ops import register_dataset # line: 860
from tensorflow.python.data.experimental.service.server_lib import DispatchServer # line: 130
from tensorflow.python.data.experimental.service.server_lib import DispatcherConfig # line: 44
from tensorflow.python.data.experimental.service.server_lib import WorkerConfig # line: 292
from tensorflow.python.data.experimental.service.server_lib import WorkerServer # line: 348
