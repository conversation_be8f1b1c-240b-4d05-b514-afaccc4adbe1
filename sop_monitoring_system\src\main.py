# 主程式入口
import cv2
import yaml
import json
import argparse
from pathlib import Path
from person_detector import PersonDetector
from action_recognizer import ActionRecognizer
from sop_monitor import SOPMonitor
from utils.video_processor import VideoProcessor
from utils.visualization import Visualizer

class SOPMonitoringSystem:
    def __init__(self, config_path="config/config.yaml"):
        # 載入配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 載入SOP時間軸
        with open("config/sop_timeline.json", 'r', encoding='utf-8') as f:
            self.sop_timeline = json.load(f)
        
        # 初始化模組
        self.person_detector = PersonDetector(self.config)
        self.action_recognizer = ActionRecognizer(self.config)
        self.sop_monitor = SOPMonitor(self.config, self.sop_timeline)
        self.video_processor = VideoProcessor(self.config)
        self.visualizer = Visualizer(self.config)
        
    def run(self, video_path):
        """運行監控系統"""
        cap = cv2.VideoCapture(video_path)
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            frame_count += 1
            timestamp = frame_count / cap.get(cv2.CAP_PROP_FPS)
            
            # 人員檢測
            persons = self.person_detector.detect(frame)
            
            # 鎖定目標作業員
            target_person = self.person_detector.get_target_person(persons)
            
            if target_person:
                # 動作識別
                action = self.action_recognizer.recognize(frame, target_person)
                
                # SOP監控
                sop_status = self.sop_monitor.check_sop_compliance(
                    action, timestamp
                )
                
                # 視覺化
                result_frame = self.visualizer.draw_results(
                    frame, target_person, action, sop_status
                )
                
                cv2.imshow('SOP Monitoring', result_frame)
                
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        cap.release()
        cv2.destroyAllWindows()

def main():
    parser = argparse.ArgumentParser(description='SOP監控系統')
    parser.add_argument('--video', required=True, help='輸入影片路徑')
    parser.add_argument('--config', default='config/config.yaml', help='配置文件路徑')
    
    args = parser.parse_args()
    
    system = SOPMonitoringSystem(args.config)
    system.run(args.video)

if __name__ == "__main__":
    main()