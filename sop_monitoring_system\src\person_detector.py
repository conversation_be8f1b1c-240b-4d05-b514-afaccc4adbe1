# 人員檢測模組
import cv2
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>
from deep_sort_realtime import DeepSort

class PersonDetector:
    def __init__(self, config):
        self.config = config
        self.model = YOLO(config['models']['pose_model'])
        self.tracker = DeepSort(
            max_age=50,
            n_init=3,
            nms_max_overlap=0.7,
            max_cosine_distance=0.4
        )
        self.target_person_id = config['monitoring']['target_person_id']
        
    def detect(self, frame):
        """檢測畫面中的人員"""
        results = self.model(frame)
        persons = []
        
        for result in results:
            if result.boxes is not None:
                for box, keypoints in zip(result.boxes, result.keypoints):
                    if box.cls == 0:  # person class
                        persons.append({
                            'bbox': box.xyxy[0].cpu().numpy(),
                            'confidence': box.conf.cpu().numpy(),
                            'keypoints': keypoints.xy[0].cpu().numpy()
                        })
        
        # 人員追踪
        if persons:
            detections = []
            for person in persons:
                bbox = person['bbox']
                detections.append([
                    bbox[0], bbox[1], bbox[2] - bbox[0], bbox[3] - bbox[1],
                    person['confidence']
                ])
            
            tracks = self.tracker.update_tracks(detections, frame=frame)
            
            # 更新persons with track IDs
            for i, track in enumerate(tracks):
                if not track.is_confirmed():
                    continue
                persons[i]['track_id'] = track.track_id
                
        return persons
    
    def get_target_person(self, persons):
        """獲取目標作業員（螺絲鎖定工）"""
        # 這裡可以加入更複雜的邏輯來識別目標人員
        # 例如：基於工作區域、工具識別等
        roi = self.config['monitoring']['roi_area']
        
        for person in persons:
            if 'track_id' in person:
                bbox = person['bbox']
                center_x = (bbox[0] + bbox[2]) / 2
                center_y = (bbox[1] + bbox[3]) / 2
                
                # 檢查是否在ROI區域內
                if (roi[0] <= center_x <= roi[2] and 
                    roi[1] <= center_y <= roi[3]):
                    return person
        
        return None