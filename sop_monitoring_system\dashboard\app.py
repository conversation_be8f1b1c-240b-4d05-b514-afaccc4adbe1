# Streamlit監控介面
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import sys
import os
import cv2

# 添加 src 目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def process_video_with_sop(video_path):
    """處理影片並進行 SOP 監控"""
    try:
        # 導入必要的模組
        from main import SOPMonitoringSystem

        # 設定配置檔案路徑
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.yaml')

        # 創建監控系統（傳入配置檔案路徑）
        system = SOPMonitoringSystem(config_path)

        # 處理影片
        st.info("🔄 正在初始化 AI 模型...")

        # 創建進度條
        progress_bar = st.progress(0)
        status_text = st.empty()

        # 開始處理
        cap = cv2.VideoCapture(video_path)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        frame_count = 0

        # 顯示影片資訊
        st.success(f"📹 影片載入成功: {total_frames} 幀, {fps:.1f} FPS")

        # 創建結果顯示區域
        result_placeholder = st.empty()
        sop_results = st.empty()

        # 初始化 SOP 監控
        system.sop_monitor.start_monitoring()

        # SOP 分析結果
        detected_actions = []
        sop_steps_completed = []
        time_analysis = []

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame_count += 1
            progress = frame_count / total_frames
            current_time = frame_count / fps

            # 更新進度
            progress_bar.progress(progress)
            status_text.text(f"處理進度: {frame_count}/{total_frames} 幀 ({progress*100:.1f}%)")

            # 處理每10幀（加速處理）
            if frame_count % 10 == 0:
                # 檢測人員
                persons = system.person_detector.detect(frame)

                # 動作識別
                if persons:
                    actions = system.action_recognizer.recognize(frame, persons)

                    # 分析 SOP 動作
                    for action in actions:
                        if action['confidence'] > 0.7:  # 高信心度的動作
                            action_type = action['action']

                            # 記錄檢測到的動作
                            detected_actions.append({
                                'time': current_time,
                                'action': action_type,
                                'confidence': action['confidence']
                            })

                            # 更新 SOP 監控
                            system.sop_monitor.update_action(
                                action_type,
                                action['confidence'],
                                action.get('person_id', 1)
                            )

                # 獲取當前 SOP 狀態
                sop_status = system.sop_monitor.get_current_status()

                # 顯示即時結果
                with result_placeholder.container():
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("檢測到人員", len(persons))
                    with col2:
                        st.metric("當前動作", sop_status.get('expected_step', '分析中'))
                    with col3:
                        st.metric("完成步驟", f"{sop_status.get('completed_steps', 0)}/19")
                    with col4:
                        st.metric("處理時間", f"{current_time:.1f}s")

                # 顯示 SOP 分析
                if len(detected_actions) > 0:
                    with sop_results.container():
                        st.subheader("🔍 即時 SOP 分析")

                        # 最近檢測到的動作
                        recent_actions = detected_actions[-5:]
                        for i, action in enumerate(recent_actions):
                            st.write(f"⏰ {action['time']:.1f}s - {action['action']} (信心度: {action['confidence']:.2f})")

        cap.release()

        # 最終分析結果
        final_status = system.sop_monitor.get_current_status()
        timeline_progress = system.sop_monitor.get_timeline_progress()

        st.success("✅ 影片處理完成！")

        # 顯示詳細分析結果
        st.header("📊 SOP 執行分析報告")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            completion_rate = (final_status.get('completed_steps', 0) / 19) * 100
            st.metric("完成率", f"{completion_rate:.1f}%")
        with col2:
            st.metric("總檢測動作", len(detected_actions))
        with col3:
            efficiency = final_status.get('stats', {}).get('time_efficiency', 0)
            st.metric("時間效率", f"{efficiency:.1f}%")
        with col4:
            alerts = len(final_status.get('recent_alerts', []))
            st.metric("異常次數", alerts)

        # 詳細步驟分析
        st.subheader("📋 步驟執行詳情")

        step_data = []
        for step in timeline_progress:
            step_data.append({
                '步驟': step['action'],
                '類型': step['type'],
                '預期時間': f"{step['start_time']}-{step['end_time']}s",
                '狀態': '✅ 完成' if step['completed'] else '❌ 未完成'
            })

        df_steps = pd.DataFrame(step_data)
        st.dataframe(df_steps, use_container_width=True)

        # 動作時間軸
        if detected_actions:
            st.subheader("⏱️ 動作時間軸")

            action_df = pd.DataFrame(detected_actions)
            fig_timeline = px.scatter(
                action_df,
                x='time',
                y='action',
                size='confidence',
                title='檢測到的動作時間軸',
                labels={'time': '時間(秒)', 'action': '動作類型'}
            )
            st.plotly_chart(fig_timeline, use_container_width=True)

        # 儲存分析結果到 session state
        st.session_state['sop_analysis'] = {
            'detected_actions': detected_actions,
            'final_status': final_status,
            'timeline_progress': timeline_progress,
            'completion_rate': completion_rate
        }

    except Exception as e:
        st.error(f"❌ 處理失敗: {str(e)}")
        st.exception(e)

# 設定頁面配置
st.set_page_config(
    page_title="SOP監控系統",
    page_icon="🔧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定義CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }
    .status-good {
        color: #28a745;
        font-weight: bold;
    }
    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }
    .status-error {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # 主標題
    st.markdown("""
    <div class="main-header">
        <h1 style="color: white; text-align: center; margin: 0;">
            🔧 螺絲鎖定SOP監控系統
        </h1>
        <p style="color: white; text-align: center; margin: 0;">
            即時監控與品質分析
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # 側邊欄
    with st.sidebar:
        st.header("📊 系統狀態")
        
        # 系統狀態
        st.metric("系統狀態", "🟢 運行中")
        st.metric("GPU使用率", "76%")
        st.metric("處理速度", "18 FPS")
        
        st.header("⚙️ 設定")
        confidence_threshold = st.slider("信心度閾值", 0.0, 1.0, 0.8)
        show_keypoints = st.checkbox("顯示關鍵點", True)
        
    # 主要內容區域
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📹 即時監控")
        
        # 影片播放區域（模擬）
        st.info("影片串流區域 - 請上傳影片開始監控")
        
        # 上傳影片
        uploaded_file = st.file_uploader("上傳測試影片", type=['mp4', 'avi', 'mov'])

        if uploaded_file is not None:
            st.video(uploaded_file)

            # 儲存上傳的影片
            if st.button("🚀 開始 SOP 監控", type="primary"):
                with st.spinner("正在處理影片..."):
                    # 儲存上傳的檔案
                    import tempfile
                    import os

                    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
                        tmp_file.write(uploaded_file.read())
                        video_path = tmp_file.name

                    # 處理影片
                    try:
                        process_video_with_sop(video_path)
                    except Exception as e:
                        st.error(f"處理影片時發生錯誤: {str(e)}")
                    finally:
                        # 清理臨時檔案
                        if os.path.exists(video_path):
                            os.unlink(video_path)

            # 顯示分析結果（如果有的話）
            if 'sop_analysis' in st.session_state:
                analysis = st.session_state['sop_analysis']

                col1_1, col1_2, col1_3 = st.columns(3)

                with col1_1:
                    last_action = analysis['detected_actions'][-1]['action'] if analysis['detected_actions'] else "無動作"
                    st.metric("最後檢測動作", last_action, "已分析")

                with col1_2:
                    completion = analysis['completion_rate']
                    st.metric("SOP完成率", f"{completion:.1f}%", f"{analysis['final_status'].get('completed_steps', 0)}/19")

                with col1_3:
                    total_actions = len(analysis['detected_actions'])
                    st.metric("檢測動作數", total_actions, "個動作")
            else:
                col1_1, col1_2, col1_3 = st.columns(3)

                with col1_1:
                    st.metric("當前動作", "等待開始", "準備中")

                with col1_2:
                    st.metric("進度", "0%", "0/19")

                with col1_3:
                    st.metric("用時", "0秒", "待開始")
    
    with col2:
        st.header("📊 SOP進度")
        
        # SOP進度圖表
        progress_data = {
            'step': [f'螺絲{i+1}' for i in range(16)] + ['旋轉1', '旋轉2', '旋轉3'],
            'status': ['完成'] * 8 + ['進行中'] + ['待執行'] * 10,
            'expected_time': [2, 1, 2, 1, 2, 2, 1, 1, 2, 1, 1, 2, 2, 1, 1, 2, 2, 1, 2],
            'actual_time': [2.1, 1.2, 1.8, 1.1, 2.3, 2.1, 1.0, 1.2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        }
        
        df = pd.DataFrame(progress_data)
        
        # 進度條
        progress_percent = (8 / 16) * 100
        st.progress(progress_percent / 100)
        st.write(f"完成進度: {progress_percent:.1f}%")
        
        # 時間分析
        st.subheader("⏱️ 時間分析")
        
        # 創建時間比較圖表
        fig_time = go.Figure()
        
        completed_steps = df[df['status'] == '完成']
        
        fig_time.add_trace(go.Bar(
            name='預期時間',
            x=completed_steps['step'],
            y=completed_steps['expected_time'],
            marker_color='lightblue'
        ))
        
        fig_time.add_trace(go.Bar(
            name='實際時間',
            x=completed_steps['step'],
            y=completed_steps['actual_time'],
            marker_color='orange'
        ))
        
        fig_time.update_layout(
            title='時間比較',
            xaxis_title='步驟',
            yaxis_title='時間(秒)',
            barmode='group',
            height=300
        )
        
        st.plotly_chart(fig_time, use_container_width=True)
    
    # 底部統計面板
    st.header("📈 統計分析")
    
    col3, col4, col5, col6 = st.columns(4)
    
    with col3:
        st.metric(
            "SOP遵循率",
            "94.2%",
            "+2.1%",
            delta_color="normal"
        )
    
    with col4:
        st.metric(
            "平均作業時間",
            "48.5秒",
            "-3.5秒",
            delta_color="inverse"
        )
    
    with col5:
        st.metric(
            "品質評分",
            "A級",
            "↑1級",
            delta_color="normal"
        )
    
    with col6:
        st.metric(
            "異常檢測",
            "0次",
            "0次",
            delta_color="normal"
        )
    
    # 詳細分析圖表
    col7, col8 = st.columns(2)
    
    with col7:
        st.subheader("📊 動作分布")
        
        # 動作分布餅圖
        action_data = {
            'action': ['鎖螺絲', '旋轉工件', '待機'],
            'count': [16, 3, 2],
            'percentage': [76.2, 14.3, 9.5]
        }
        
        fig_pie = px.pie(
            values=action_data['count'],
            names=action_data['action'],
            title='動作分布'
        )
        
        st.plotly_chart(fig_pie, use_container_width=True)
    
    with col8:
        st.subheader("📈 效率趨勢")
        
        # 效率趨勢圖
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        efficiency = np.random.normal(85, 10, 30)
        efficiency = np.clip(efficiency, 60, 100)
        
        fig_trend = go.Figure()
        fig_trend.add_trace(go.Scatter(
            x=dates,
            y=efficiency,
            mode='lines+markers',
            name='效率',
            line=dict(color='green', width=2)
        ))
        
        fig_trend.update_layout(
            title='30天效率趨勢',
            xaxis_title='日期',
            yaxis_title='效率(%)',
            height=300
        )
        
        st.plotly_chart(fig_trend, use_container_width=True)

if __name__ == "__main__":
    main()