//===- RuntimeVerifiableOpInterface.h - Op Verification ---------*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef MLIR_INTERFACES_RUNTIMEVERIFIABLEOPINTERFACE_H_
#define MLIR_INTERFACES_RUNTIMEVERIFIABLEOPINTERFACE_H_

#include "mlir/IR/OpDefinition.h"

/// Include the generated interface declarations.
#include "mlir/Interfaces/RuntimeVerifiableOpInterface.h.inc"

#endif // MLIR_INTERFACES_RUNTIMEVERIFIABLEOPINTERFACE_H_
