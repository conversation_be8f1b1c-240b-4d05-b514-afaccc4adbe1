//===- TransformAttr.h - Transform Dialect Attribute Definition -*- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#ifndef MLIR_DIALECT_TRANSFORM_IR_TRANSFORMATTRS_H
#define MLIR_DIALECT_TRANSFORM_IR_TRANSFORMATTRS_H

#include "mlir/IR/Attributes.h"
#include "mlir/IR/BuiltinAttributes.h"

#include <cstdint>
#include <optional>

#include "mlir/Dialect/Transform/IR/TransformDialectEnums.h.inc"

#endif // MLIR_DIALECT_TRANSFORM_IR_TRANSFORMATTRS_H
