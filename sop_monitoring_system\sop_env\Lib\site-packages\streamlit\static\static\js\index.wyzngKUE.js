import{r as i,j as c,bp as g,bN as b}from"./index.C1NIn1Y2.js";import{a as m}from"./useBasicWidgetState.Ci89jaH5.js";import"./FormClearHelper.D1M9GM_c.js";const p=(t,e)=>t.getStringValue(e),f=t=>t.default??null,C=t=>t.value??null,d=(t,e,r,o)=>{e.setStringValue(t,r.value,{fromUi:r.fromUi},o)},S=({element:t,disabled:e,widgetMgr:r,fragmentId:o})=>{var l;const[s,a]=m({getStateFromWidgetMgr:p,getDefaultStateFromProto:f,getCurrStateFromProto:C,updateWidgetMgrState:d,element:t,widgetMgr:r,fragmentId:o}),u=i.useCallback(n=>{a({value:n,fromUi:!0})},[a]);return c(b,{label:t.label,labelVisibility:g((l=t.labelVisibility)==null?void 0:l.value),help:t.help,onChange:u,disabled:e,value:s})},k=i.memo(S);export{k as default};
