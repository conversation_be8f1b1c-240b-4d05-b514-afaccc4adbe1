/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_LOWERVECTORMASKPASS
#define GEN_PASS_DECL_LOWERVECTORMULTIREDUCTION
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// LowerVectorMaskPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERVECTORMASKPASS
#undef GEN_PASS_DECL_LOWERVECTORMASKPASS
#endif // GEN_PASS_DECL_LOWERVECTORMASKPASS
#ifdef GEN_PASS_DEF_LOWERVECTORMASKPASS
namespace impl {

template <typename DerivedT>
class LowerVectorMaskPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LowerVectorMaskPassBase;

  LowerVectorMaskPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVectorMaskPassBase(const LowerVectorMaskPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LowerVectorMaskPassBase& operator=(const LowerVectorMaskPassBase &) = delete;
  LowerVectorMaskPassBase(LowerVectorMaskPassBase &&) = delete;
  LowerVectorMaskPassBase& operator=(LowerVectorMaskPassBase &&) = delete;
  ~LowerVectorMaskPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-vector-mask");
  }
  ::llvm::StringRef getArgument() const override { return "lower-vector-mask"; }

  ::llvm::StringRef getDescription() const override { return "Lower 'vector.mask' operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVectorMaskPass");
  }
  ::llvm::StringRef getName() const override { return "LowerVectorMaskPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVectorMaskPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOWERVECTORMASKPASS
#endif // GEN_PASS_DEF_LOWERVECTORMASKPASS

//===----------------------------------------------------------------------===//
// LowerVectorMultiReduction
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LOWERVECTORMULTIREDUCTION
struct LowerVectorMultiReductionOptions {
  mlir::vector::VectorMultiReductionLowering loweringStrategy = mlir::vector::VectorMultiReductionLowering::InnerParallel;
};
#undef GEN_PASS_DECL_LOWERVECTORMULTIREDUCTION
#endif // GEN_PASS_DECL_LOWERVECTORMULTIREDUCTION
#ifdef GEN_PASS_DEF_LOWERVECTORMULTIREDUCTION
namespace impl {

template <typename DerivedT>
class LowerVectorMultiReductionBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LowerVectorMultiReductionBase;

  LowerVectorMultiReductionBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVectorMultiReductionBase(const LowerVectorMultiReductionBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LowerVectorMultiReductionBase& operator=(const LowerVectorMultiReductionBase &) = delete;
  LowerVectorMultiReductionBase(LowerVectorMultiReductionBase &&) = delete;
  LowerVectorMultiReductionBase& operator=(LowerVectorMultiReductionBase &&) = delete;
  ~LowerVectorMultiReductionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-vector-multi-reduction");
  }
  ::llvm::StringRef getArgument() const override { return "lower-vector-multi-reduction"; }

  ::llvm::StringRef getDescription() const override { return "Lower 'vector.multi_reduction' operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVectorMultiReduction");
  }
  ::llvm::StringRef getName() const override { return "LowerVectorMultiReduction"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVectorMultiReductionBase<DerivedT>)

  LowerVectorMultiReductionBase(LowerVectorMultiReductionOptions options) : LowerVectorMultiReductionBase() {
    loweringStrategy = std::move(options.loweringStrategy);
  }
protected:
  ::mlir::Pass::Option<mlir::vector::VectorMultiReductionLowering> loweringStrategy{*this, "lowering-strategy", ::llvm::cl::desc("Select the strategy to control how multi_reduction is lowered."), ::llvm::cl::init(mlir::vector::VectorMultiReductionLowering::InnerParallel), ::llvm::cl::values(
            clEnumValN(mlir::vector::VectorMultiReductionLowering::InnerParallel,
                       "inner-parallel",
                       "Lower multi_reduction into outer-reduction and inner-parallel ops."),
            clEnumValN(mlir::vector::VectorMultiReductionLowering::InnerReduction,
                       "inner-reduction",
                       "Lower multi_reduction into outer-parallel and inner-reduction ops.")
        )};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LOWERVECTORMULTIREDUCTION
#endif // GEN_PASS_DEF_LOWERVECTORMULTIREDUCTION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// LowerVectorMaskPass Registration
//===----------------------------------------------------------------------===//

inline void registerLowerVectorMaskPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createLowerVectorMaskPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerVectorMaskPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createLowerVectorMaskPass();
  });
}

//===----------------------------------------------------------------------===//
// LowerVectorMultiReduction Registration
//===----------------------------------------------------------------------===//

inline void registerLowerVectorMultiReduction() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createLowerVectorMultiReductionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLowerVectorMultiReductionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::vector::createLowerVectorMultiReductionPass();
  });
}

//===----------------------------------------------------------------------===//
// Vector Registration
//===----------------------------------------------------------------------===//

inline void registerVectorPasses() {
  registerLowerVectorMaskPass();
  registerLowerVectorMultiReduction();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class LowerVectorMaskPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LowerVectorMaskPassBase;

  LowerVectorMaskPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVectorMaskPassBase(const LowerVectorMaskPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LowerVectorMaskPassBase& operator=(const LowerVectorMaskPassBase &) = delete;
  LowerVectorMaskPassBase(LowerVectorMaskPassBase &&) = delete;
  LowerVectorMaskPassBase& operator=(LowerVectorMaskPassBase &&) = delete;
  ~LowerVectorMaskPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-vector-mask");
  }
  ::llvm::StringRef getArgument() const override { return "lower-vector-mask"; }

  ::llvm::StringRef getDescription() const override { return "Lower 'vector.mask' operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVectorMaskPass");
  }
  ::llvm::StringRef getName() const override { return "LowerVectorMaskPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVectorMaskPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LowerVectorMultiReductionBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = LowerVectorMultiReductionBase;

  LowerVectorMultiReductionBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LowerVectorMultiReductionBase(const LowerVectorMultiReductionBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  LowerVectorMultiReductionBase& operator=(const LowerVectorMultiReductionBase &) = delete;
  LowerVectorMultiReductionBase(LowerVectorMultiReductionBase &&) = delete;
  LowerVectorMultiReductionBase& operator=(LowerVectorMultiReductionBase &&) = delete;
  ~LowerVectorMultiReductionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("lower-vector-multi-reduction");
  }
  ::llvm::StringRef getArgument() const override { return "lower-vector-multi-reduction"; }

  ::llvm::StringRef getDescription() const override { return "Lower 'vector.multi_reduction' operations"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LowerVectorMultiReduction");
  }
  ::llvm::StringRef getName() const override { return "LowerVectorMultiReduction"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LowerVectorMultiReductionBase<DerivedT>)

protected:
  ::mlir::Pass::Option<mlir::vector::VectorMultiReductionLowering> loweringStrategy{*this, "lowering-strategy", ::llvm::cl::desc("Select the strategy to control how multi_reduction is lowered."), ::llvm::cl::init(mlir::vector::VectorMultiReductionLowering::InnerParallel), ::llvm::cl::values(
            clEnumValN(mlir::vector::VectorMultiReductionLowering::InnerParallel,
                       "inner-parallel",
                       "Lower multi_reduction into outer-reduction and inner-parallel ops."),
            clEnumValN(mlir::vector::VectorMultiReductionLowering::InnerReduction,
                       "inner-reduction",
                       "Lower multi_reduction into outer-parallel and inner-reduction ops.")
        )};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
