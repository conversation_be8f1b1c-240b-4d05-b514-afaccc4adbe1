# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/gpu/copy_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#mediapipe/gpu/copy_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xd7\x01\n\x15\x43opyCalculatorOptions\x12\x41\n\x08rotation\x18\x01 \x01(\x0e\x32).mediapipe.CopyCalculatorOptions.Rotation:\x04NONE\"+\n\x08Rotation\x12\x08\n\x04NONE\x10\x00\x12\x07\n\x03\x43\x43W\x10\x01\x12\x0c\n\x08\x43\x43W_FLIP\x10\x02\x32N\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xb4\xf2\x91M \x01(\x0b\x32 .mediapipe.CopyCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.gpu.copy_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_COPYCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_COPYCALCULATOROPTIONS']._serialized_start=89
  _globals['_COPYCALCULATOROPTIONS']._serialized_end=304
  _globals['_COPYCALCULATOROPTIONS_ROTATION']._serialized_start=181
  _globals['_COPYCALCULATOROPTIONS_ROTATION']._serialized_end=224
# @@protoc_insertion_point(module_scope)
