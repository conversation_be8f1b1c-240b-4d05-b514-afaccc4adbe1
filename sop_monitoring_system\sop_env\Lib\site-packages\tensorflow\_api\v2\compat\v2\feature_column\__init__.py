# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.feature_column namespace
"""

import sys as _sys

from tensorflow.python.feature_column.feature_column_v2 import bucketized_column # line: 918
from tensorflow.python.feature_column.feature_column_v2 import categorical_column_with_hash_bucket # line: 1005
from tensorflow.python.feature_column.feature_column_v2 import categorical_column_with_identity # line: 1442
from tensorflow.python.feature_column.feature_column_v2 import categorical_column_with_vocabulary_file_v2 as categorical_column_with_vocabulary_file # line: 1185
from tensorflow.python.feature_column.feature_column_v2 import categorical_column_with_vocabulary_list # line: 1323
from tensorflow.python.feature_column.feature_column_v2 import crossed_column # line: 1643
from tensorflow.python.feature_column.feature_column_v2 import embedding_column # line: 436
from tensorflow.python.feature_column.feature_column_v2 import indicator_column # line: 1520
from tensorflow.python.feature_column.feature_column_v2 import make_parse_example_spec_v2 as make_parse_example_spec # line: 371
from tensorflow.python.feature_column.feature_column_v2 import numeric_column # line: 818
from tensorflow.python.feature_column.feature_column_v2 import shared_embedding_columns_v2 as shared_embeddings # line: 676
from tensorflow.python.feature_column.feature_column_v2 import weighted_categorical_column # line: 1565
from tensorflow.python.feature_column.sequence_feature_column import sequence_categorical_column_with_hash_bucket # line: 153
from tensorflow.python.feature_column.sequence_feature_column import sequence_categorical_column_with_identity # line: 103
from tensorflow.python.feature_column.sequence_feature_column import sequence_categorical_column_with_vocabulary_file # line: 200
from tensorflow.python.feature_column.sequence_feature_column import sequence_categorical_column_with_vocabulary_list # line: 270
from tensorflow.python.feature_column.sequence_feature_column import sequence_numeric_column # line: 337
