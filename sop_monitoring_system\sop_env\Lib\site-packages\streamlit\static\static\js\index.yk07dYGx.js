import{n as p,r as s,z as T,C as y,j as i,D as w,aC as v,aR as r,F as h,B as m,aS as R,aT as C}from"./index.C1NIn1Y2.js";const L=p("button",{target:"ee3yv4c0"})(({theme:o})=>({fontSize:o.fontSizes.sm,lineHeight:o.lineHeights.base,color:o.colors.fadedText60,backgroundColor:o.colors.transparent,fontFamily:"inherit",margin:o.spacing.none,border:"none",boxShadow:"none",padding:o.spacing.none,"&:hover, &:active, &:focus":{border:"none",outline:"none",boxShadow:"none"},"&:hover":{color:o.colors.primary}})),B=p("div",{target:"ee3yv4c1"})(({theme:o})=>({display:"flex",flexDirection:"row",gap:o.spacing.lg,"> span":{marginTop:o.spacing.twoXS}})),k=p("div",{target:"ee3yv4c2"})(({theme:o})=>({display:"flex",flexDirection:"column",gap:o.spacing.sm,alignItems:"start",justifyContent:"center",overflow:"hidden",minHeight:"100%",fontSize:o.fontSizes.sm,lineHeight:o.lineHeights.base,div:{display:"inline-flex"}}));function z(o){const a=m(o);return{Body:{props:{"data-testid":"stToast",className:"stToast"},style:{display:"flex",flexDirection:"row",gap:o.spacing.md,width:o.sizes.toastWidth,marginTop:o.spacing.sm,borderTopLeftRadius:o.radii.default,borderTopRightRadius:o.radii.default,borderBottomLeftRadius:o.radii.default,borderBottomRightRadius:o.radii.default,paddingTop:o.spacing.lg,paddingBottom:o.spacing.lg,paddingLeft:o.spacing.twoXL,paddingRight:o.spacing.twoXL,backgroundColor:a?o.colors.gray10:o.colors.gray90,color:o.colors.bodyText,boxShadow:a?"0px 4px 16px rgba(0, 0, 0, 0.16)":"0px 4px 16px rgba(0, 0, 0, 0.7)"}},CloseIcon:{style:{color:o.colors.fadedText40,width:o.fontSizes.lg,height:o.fontSizes.lg,marginRight:`calc(-1 * ${o.spacing.lg} / 2)`,":hover":{color:o.colors.bodyText}}}}}function E(o){if(o.length>104){let t=o.replace(/^(.{104}[^\s]*).*/,"$1");return t.length>104&&(t=t.substring(0,104).split(" ").slice(0,-1).join(" ")),t.trim()}return o}function D({body:o,icon:a}){const t=T(),n=E(o),d=o!==n,[e,x]=s.useState(!d),[u,b]=s.useState(0),f=s.useCallback(()=>{x(!e)},[e]),c=s.useMemo(()=>z(t),[t]),l=s.useMemo(()=>y(B,{expanded:e,children:[a&&i(w,{iconValue:a,size:"xl",testid:"stToastDynamicIcon"}),y(k,{children:[i(v,{source:e?o:n,allowHTML:!1,isToast:!0}),d&&i(L,{"data-testid":"stToastViewButton",onClick:f,children:e?"view less":"view more"})]})]}),[d,e,o,a,n,f]);s.useEffect(()=>{if(t.inSidebar)return;const g=r.info(l,{overrides:{...c}});return b(g),()=>{r.update(g,{overrides:{Body:{style:{display:"none"}}}}),r.clear(g)}},[]),s.useEffect(()=>{r.update(u,{children:l,overrides:{...c}})},[u,l,c]);const S=i(C,{kind:R.ERROR,body:"Streamlit API Error: `st.toast` cannot be called directly on the sidebar with `st.sidebar.toast`. See our `st.toast` API [docs](https://docs.streamlit.io/develop/api-reference/status/st.toast) for more information."});return i(h,{children:t.inSidebar&&S})}const I=s.memo(D);export{I as default};
