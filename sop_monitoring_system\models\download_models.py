#!/usr/bin/env python3
"""
模型下載腳本
下載 SOP 監控系統所需的 AI 模型
"""

import os
import sys
from pathlib import Path
from ultralytics import YOLO
import urllib.request
import zipfile

def download_yolo_models():
    """下載 YOLO 模型"""
    print("正在下載 YOLO 姿態檢測模型...")

    # 確保在 models 目錄中
    models_dir = Path(__file__).parent
    os.chdir(models_dir)

    try:
        # 下載 YOLOv11 姿態檢測模型
        print("下載 yolo11n-pose.pt...")
        model = YOLO('yolo11n-pose.pt')  # 這會自動下載模型
        print("✅ yolo11n-pose.pt 下載完成")

        # 下載 YOLOv11 物件檢測模型 (用於人員檢測)
        print("下載 yolo11n.pt...")
        model = YOLO('yolo11n.pt')  # 這會自動下載模型
        print("✅ yolo11n.pt 下載完成")

        return True

    except Exception as e:
        print(f"❌ YOLO 模型下載失敗: {e}")
        return False

def verify_models():
    """驗證模型是否下載成功"""
    models_dir = Path(__file__).parent
    required_models = [
        'yolo11n-pose.pt',
        'yolo11n.pt'
    ]

    print("\n驗證模型檔案...")
    all_present = True

    for model_file in required_models:
        model_path = models_dir / model_file
        if model_path.exists():
            size_mb = model_path.stat().st_size / (1024 * 1024)
            print(f"✅ {model_file} - {size_mb:.1f} MB")
        else:
            print(f"❌ {model_file} - 檔案不存在")
            all_present = False

    return all_present

def main():
    """主函數"""
    print("=== SOP 監控系統模型下載器 ===\n")

    # 下載模型
    success = download_yolo_models()

    if success:
        # 驗證模型
        if verify_models():
            print("\n🎉 所有模型下載完成！")
            print("您現在可以運行 SOP 監控系統了。")
        else:
            print("\n⚠️ 部分模型檔案可能有問題，請重新下載。")
            sys.exit(1)
    else:
        print("\n❌ 模型下載失敗，請檢查網路連線。")
        sys.exit(1)

if __name__ == "__main__":
    main()
