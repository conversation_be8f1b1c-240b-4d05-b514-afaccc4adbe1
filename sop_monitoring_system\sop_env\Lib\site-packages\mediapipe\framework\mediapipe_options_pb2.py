# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/mediapipe_options.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+mediapipe/framework/mediapipe_options.proto\x12\tmediapipe\"\x1e\n\x10MediaPipeOptions*\n\x08\xa0\x9c\x01\x10\x80\x80\x80\x80\x02\x42\x33\n\x1a\x63om.google.mediapipe.protoB\x15MediaPipeOptionsProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.mediapipe_options_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\025MediaPipeOptionsProto'
  _globals['_MEDIAPIPEOPTIONS']._serialized_start=58
  _globals['_MEDIAPIPEOPTIONS']._serialized_end=88
# @@protoc_insertion_point(module_scope)
