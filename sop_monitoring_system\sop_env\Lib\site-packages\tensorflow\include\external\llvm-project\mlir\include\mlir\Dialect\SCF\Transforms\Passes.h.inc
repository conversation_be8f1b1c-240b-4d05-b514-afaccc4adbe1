/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#define GEN_PASS_DECL_SCFFORLOOPPEELING
#define GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#define GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#define GEN_PASS_DECL_SCFFORTOWHILELOOP
#define GEN_PASS_DECL_SCFFORALLTOFORLOOP
#define GEN_PASS_DECL_SCFFORALLTOPARALLELLOOP
#define GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#define GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#define GEN_PASS_DECL_SCFPARALLELLOOPTILING
#define GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// SCFForLoopCanonicalization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#undef GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#endif // GEN_PASS_DECL_SCFFORLOOPCANONICALIZATION
#ifdef GEN_PASS_DEF_SCFFORLOOPCANONICALIZATION
namespace impl {

template <typename DerivedT>
class SCFForLoopCanonicalizationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopCanonicalizationBase;

  SCFForLoopCanonicalizationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopCanonicalizationBase(const SCFForLoopCanonicalizationBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopCanonicalizationBase& operator=(const SCFForLoopCanonicalizationBase &) = delete;
  SCFForLoopCanonicalizationBase(SCFForLoopCanonicalizationBase &&) = delete;
  SCFForLoopCanonicalizationBase& operator=(SCFForLoopCanonicalizationBase &&) = delete;
  ~SCFForLoopCanonicalizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-canonicalization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-canonicalization"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations within scf.for loop bodies"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopCanonicalization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopCanonicalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<tensor::TensorDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopCanonicalizationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPCANONICALIZATION
#endif // GEN_PASS_DEF_SCFFORLOOPCANONICALIZATION

//===----------------------------------------------------------------------===//
// SCFForLoopPeeling
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPPEELING
struct SCFForLoopPeelingOptions {
  bool peelFront = false;
  bool skipPartial = true;
};
#undef GEN_PASS_DECL_SCFFORLOOPPEELING
#endif // GEN_PASS_DECL_SCFFORLOOPPEELING
#ifdef GEN_PASS_DEF_SCFFORLOOPPEELING
namespace impl {

template <typename DerivedT>
class SCFForLoopPeelingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopPeelingBase;

  SCFForLoopPeelingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopPeelingBase(const SCFForLoopPeelingBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopPeelingBase& operator=(const SCFForLoopPeelingBase &) = delete;
  SCFForLoopPeelingBase(SCFForLoopPeelingBase &&) = delete;
  SCFForLoopPeelingBase& operator=(SCFForLoopPeelingBase &&) = delete;
  ~SCFForLoopPeelingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-peeling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-peeling"; }

  ::llvm::StringRef getDescription() const override { return "Peel `for` loops at their upper bounds."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopPeeling");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopPeeling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopPeelingBase<DerivedT>)

  SCFForLoopPeelingBase(SCFForLoopPeelingOptions options) : SCFForLoopPeelingBase() {
    peelFront = std::move(options.peelFront);
    skipPartial = std::move(options.skipPartial);
  }
protected:
  ::mlir::Pass::Option<bool> peelFront{*this, "peel-front", ::llvm::cl::desc("Peel the first iteration out of the loop."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> skipPartial{*this, "skip-partial", ::llvm::cl::desc("Do not peel loops inside of the last, partial iteration of another already peeled loop."), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPPEELING
#endif // GEN_PASS_DEF_SCFFORLOOPPEELING

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#undef GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#endif // GEN_PASS_DECL_SCFFORLOOPRANGEFOLDING
#ifdef GEN_PASS_DEF_SCFFORLOOPRANGEFOLDING
namespace impl {

template <typename DerivedT>
class SCFForLoopRangeFoldingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopRangeFoldingBase;

  SCFForLoopRangeFoldingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopRangeFoldingBase(const SCFForLoopRangeFoldingBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopRangeFoldingBase& operator=(const SCFForLoopRangeFoldingBase &) = delete;
  SCFForLoopRangeFoldingBase(SCFForLoopRangeFoldingBase &&) = delete;
  SCFForLoopRangeFoldingBase& operator=(SCFForLoopRangeFoldingBase &&) = delete;
  ~SCFForLoopRangeFoldingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-range-folding");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-range-folding"; }

  ::llvm::StringRef getDescription() const override { return "Fold add/mul ops into loop range"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopRangeFolding");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopRangeFolding"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopRangeFoldingBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPRANGEFOLDING
#endif // GEN_PASS_DEF_SCFFORLOOPRANGEFOLDING

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#undef GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#endif // GEN_PASS_DECL_SCFFORLOOPSPECIALIZATION
#ifdef GEN_PASS_DEF_SCFFORLOOPSPECIALIZATION
namespace impl {

template <typename DerivedT>
class SCFForLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopSpecializationBase;

  SCFForLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopSpecializationBase(const SCFForLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopSpecializationBase& operator=(const SCFForLoopSpecializationBase &) = delete;
  SCFForLoopSpecializationBase(SCFForLoopSpecializationBase &&) = delete;
  SCFForLoopSpecializationBase& operator=(SCFForLoopSpecializationBase &&) = delete;
  ~SCFForLoopSpecializationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize `for` loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopSpecializationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORLOOPSPECIALIZATION
#endif // GEN_PASS_DEF_SCFFORLOOPSPECIALIZATION

//===----------------------------------------------------------------------===//
// SCFForToWhileLoop
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORTOWHILELOOP
#undef GEN_PASS_DECL_SCFFORTOWHILELOOP
#endif // GEN_PASS_DECL_SCFFORTOWHILELOOP
#ifdef GEN_PASS_DEF_SCFFORTOWHILELOOP
namespace impl {

template <typename DerivedT>
class SCFForToWhileLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForToWhileLoopBase;

  SCFForToWhileLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForToWhileLoopBase(const SCFForToWhileLoopBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForToWhileLoopBase& operator=(const SCFForToWhileLoopBase &) = delete;
  SCFForToWhileLoopBase(SCFForToWhileLoopBase &&) = delete;
  SCFForToWhileLoopBase& operator=(SCFForToWhileLoopBase &&) = delete;
  ~SCFForToWhileLoopBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-to-while");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-to-while"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF for loops to SCF while loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForToWhileLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForToWhileLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForToWhileLoopBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORTOWHILELOOP
#endif // GEN_PASS_DEF_SCFFORTOWHILELOOP

//===----------------------------------------------------------------------===//
// SCFForallToForLoop
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORALLTOFORLOOP
#undef GEN_PASS_DECL_SCFFORALLTOFORLOOP
#endif // GEN_PASS_DECL_SCFFORALLTOFORLOOP
#ifdef GEN_PASS_DEF_SCFFORALLTOFORLOOP
namespace impl {

template <typename DerivedT>
class SCFForallToForLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForallToForLoopBase;

  SCFForallToForLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForallToForLoopBase(const SCFForallToForLoopBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForallToForLoopBase& operator=(const SCFForallToForLoopBase &) = delete;
  SCFForallToForLoopBase(SCFForallToForLoopBase &&) = delete;
  SCFForallToForLoopBase& operator=(SCFForallToForLoopBase &&) = delete;
  ~SCFForallToForLoopBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-forall-to-for");
  }
  ::llvm::StringRef getArgument() const override { return "scf-forall-to-for"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF forall loops to SCF for loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForallToForLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForallToForLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForallToForLoopBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORALLTOFORLOOP
#endif // GEN_PASS_DEF_SCFFORALLTOFORLOOP

//===----------------------------------------------------------------------===//
// SCFForallToParallelLoop
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFFORALLTOPARALLELLOOP
#undef GEN_PASS_DECL_SCFFORALLTOPARALLELLOOP
#endif // GEN_PASS_DECL_SCFFORALLTOPARALLELLOOP
#ifdef GEN_PASS_DEF_SCFFORALLTOPARALLELLOOP
namespace impl {

template <typename DerivedT>
class SCFForallToParallelLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForallToParallelLoopBase;

  SCFForallToParallelLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForallToParallelLoopBase(const SCFForallToParallelLoopBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForallToParallelLoopBase& operator=(const SCFForallToParallelLoopBase &) = delete;
  SCFForallToParallelLoopBase(SCFForallToParallelLoopBase &&) = delete;
  SCFForallToParallelLoopBase& operator=(SCFForallToParallelLoopBase &&) = delete;
  ~SCFForallToParallelLoopBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-forall-to-parallel");
  }
  ::llvm::StringRef getArgument() const override { return "scf-forall-to-parallel"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF forall loops to SCF parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForallToParallelLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForallToParallelLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForallToParallelLoopBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFFORALLTOPARALLELLOOP
#endif // GEN_PASS_DEF_SCFFORALLTOPARALLELLOOP

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#undef GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#endif // GEN_PASS_DECL_SCFPARALLELLOOPFUSION
#ifdef GEN_PASS_DEF_SCFPARALLELLOOPFUSION
namespace impl {

template <typename DerivedT>
class SCFParallelLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopFusionBase;

  SCFParallelLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopFusionBase(const SCFParallelLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}
  SCFParallelLoopFusionBase& operator=(const SCFParallelLoopFusionBase &) = delete;
  SCFParallelLoopFusionBase(SCFParallelLoopFusionBase &&) = delete;
  SCFParallelLoopFusionBase& operator=(SCFParallelLoopFusionBase &&) = delete;
  ~SCFParallelLoopFusionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse adjacent parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopFusionBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFPARALLELLOOPFUSION
#endif // GEN_PASS_DEF_SCFPARALLELLOOPFUSION

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#undef GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#endif // GEN_PASS_DECL_SCFPARALLELLOOPSPECIALIZATION
#ifdef GEN_PASS_DEF_SCFPARALLELLOOPSPECIALIZATION
namespace impl {

template <typename DerivedT>
class SCFParallelLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopSpecializationBase;

  SCFParallelLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopSpecializationBase(const SCFParallelLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}
  SCFParallelLoopSpecializationBase& operator=(const SCFParallelLoopSpecializationBase &) = delete;
  SCFParallelLoopSpecializationBase(SCFParallelLoopSpecializationBase &&) = delete;
  SCFParallelLoopSpecializationBase& operator=(SCFParallelLoopSpecializationBase &&) = delete;
  ~SCFParallelLoopSpecializationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize parallel loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopSpecializationBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFPARALLELLOOPSPECIALIZATION
#endif // GEN_PASS_DEF_SCFPARALLELLOOPSPECIALIZATION

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SCFPARALLELLOOPTILING
struct SCFParallelLoopTilingOptions {
  ::llvm::SmallVector<int64_t> tileSizes;
  bool noMinMaxBounds = false;
};
#undef GEN_PASS_DECL_SCFPARALLELLOOPTILING
#endif // GEN_PASS_DECL_SCFPARALLELLOOPTILING
#ifdef GEN_PASS_DEF_SCFPARALLELLOOPTILING
namespace impl {

template <typename DerivedT>
class SCFParallelLoopTilingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopTilingBase;

  SCFParallelLoopTilingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopTilingBase(const SCFParallelLoopTilingBase &other) : ::mlir::OperationPass<>(other) {}
  SCFParallelLoopTilingBase& operator=(const SCFParallelLoopTilingBase &) = delete;
  SCFParallelLoopTilingBase(SCFParallelLoopTilingBase &&) = delete;
  SCFParallelLoopTilingBase& operator=(SCFParallelLoopTilingBase &&) = delete;
  ~SCFParallelLoopTilingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-tiling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-tiling"; }

  ::llvm::StringRef getDescription() const override { return "Tile parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopTilingBase<DerivedT>)

  SCFParallelLoopTilingBase(SCFParallelLoopTilingOptions options) : SCFParallelLoopTilingBase() {
    tileSizes = std::move(options.tileSizes);
    noMinMaxBounds = std::move(options.noMinMaxBounds);
  }
protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "parallel-loop-tile-sizes", ::llvm::cl::desc("Factors to tile parallel loops by")};
  ::mlir::Pass::Option<bool> noMinMaxBounds{*this, "no-min-max-bounds", ::llvm::cl::desc("Perform tiling with fixed upper bound with inbound check inside the internal loops"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_SCFPARALLELLOOPTILING
#endif // GEN_PASS_DEF_SCFPARALLELLOOPTILING

//===----------------------------------------------------------------------===//
// TestSCFParallelLoopCollapsing
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
struct TestSCFParallelLoopCollapsingOptions {
  ::llvm::SmallVector<unsigned> clCollapsedIndices0;
  ::llvm::SmallVector<unsigned> clCollapsedIndices1;
  ::llvm::SmallVector<unsigned> clCollapsedIndices2;
};
#undef GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
#endif // GEN_PASS_DECL_TESTSCFPARALLELLOOPCOLLAPSING
#ifdef GEN_PASS_DEF_TESTSCFPARALLELLOOPCOLLAPSING
namespace impl {

template <typename DerivedT>
class TestSCFParallelLoopCollapsingBase : public ::mlir::OperationPass<> {
public:
  using Base = TestSCFParallelLoopCollapsingBase;

  TestSCFParallelLoopCollapsingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TestSCFParallelLoopCollapsingBase(const TestSCFParallelLoopCollapsingBase &other) : ::mlir::OperationPass<>(other) {}
  TestSCFParallelLoopCollapsingBase& operator=(const TestSCFParallelLoopCollapsingBase &) = delete;
  TestSCFParallelLoopCollapsingBase(TestSCFParallelLoopCollapsingBase &&) = delete;
  TestSCFParallelLoopCollapsingBase& operator=(TestSCFParallelLoopCollapsingBase &&) = delete;
  ~TestSCFParallelLoopCollapsingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("test-scf-parallel-loop-collapsing");
  }
  ::llvm::StringRef getArgument() const override { return "test-scf-parallel-loop-collapsing"; }

  ::llvm::StringRef getDescription() const override { return "Test parallel loops collapsing transformation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestSCFParallelLoopCollapsing");
  }
  ::llvm::StringRef getName() const override { return "TestSCFParallelLoopCollapsing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestSCFParallelLoopCollapsingBase<DerivedT>)

  TestSCFParallelLoopCollapsingBase(TestSCFParallelLoopCollapsingOptions options) : TestSCFParallelLoopCollapsingBase() {
    clCollapsedIndices0 = std::move(options.clCollapsedIndices0);
    clCollapsedIndices1 = std::move(options.clCollapsedIndices1);
    clCollapsedIndices2 = std::move(options.clCollapsedIndices2);
  }
protected:
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices0{*this, "collapsed-indices-0", ::llvm::cl::desc("Which loop indices to combine 0th loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices1{*this, "collapsed-indices-1", ::llvm::cl::desc("Which loop indices to combine into the position 1 loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices2{*this, "collapsed-indices-2", ::llvm::cl::desc("Which loop indices to combine into the position 2 loop index")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TESTSCFPARALLELLOOPCOLLAPSING
#endif // GEN_PASS_DEF_TESTSCFPARALLELLOOPCOLLAPSING
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// SCFForLoopCanonicalization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopCanonicalization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFForLoopCanonicalizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopCanonicalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createSCFForLoopCanonicalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopPeeling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopPeeling() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopPeelingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopPeelingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopPeelingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopRangeFolding Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopRangeFolding() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopRangeFoldingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopRangeFoldingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopRangeFoldingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForLoopSpecialization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopSpecializationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForToWhileLoop Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForToWhileLoop() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForToWhileLoopPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForToWhileLoopPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForToWhileLoopPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForallToForLoop Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForallToForLoop() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForallToForLoopPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForallToForLoopPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForallToForLoopPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFForallToParallelLoop Registration
//===----------------------------------------------------------------------===//

inline void registerSCFForallToParallelLoop() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForallToParallelLoopPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFForallToParallelLoopPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createForallToParallelLoopPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopFusion Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopFusion() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopFusionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFParallelLoopFusionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopFusionPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopSpecialization Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopSpecialization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopSpecializationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFParallelLoopSpecializationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopSpecializationPass();
  });
}

//===----------------------------------------------------------------------===//
// SCFParallelLoopTiling Registration
//===----------------------------------------------------------------------===//

inline void registerSCFParallelLoopTiling() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopTilingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSCFParallelLoopTilingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createParallelLoopTilingPass();
  });
}

//===----------------------------------------------------------------------===//
// TestSCFParallelLoopCollapsing Registration
//===----------------------------------------------------------------------===//

inline void registerTestSCFParallelLoopCollapsing() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTestSCFParallelLoopCollapsingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTestSCFParallelLoopCollapsingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createTestSCFParallelLoopCollapsingPass();
  });
}

//===----------------------------------------------------------------------===//
// SCF Registration
//===----------------------------------------------------------------------===//

inline void registerSCFPasses() {
  registerSCFForLoopCanonicalization();
  registerSCFForLoopPeeling();
  registerSCFForLoopRangeFolding();
  registerSCFForLoopSpecialization();
  registerSCFForToWhileLoop();
  registerSCFForallToForLoop();
  registerSCFForallToParallelLoop();
  registerSCFParallelLoopFusion();
  registerSCFParallelLoopSpecialization();
  registerSCFParallelLoopTiling();
  registerTestSCFParallelLoopCollapsing();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class SCFForLoopCanonicalizationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopCanonicalizationBase;

  SCFForLoopCanonicalizationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopCanonicalizationBase(const SCFForLoopCanonicalizationBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopCanonicalizationBase& operator=(const SCFForLoopCanonicalizationBase &) = delete;
  SCFForLoopCanonicalizationBase(SCFForLoopCanonicalizationBase &&) = delete;
  SCFForLoopCanonicalizationBase& operator=(SCFForLoopCanonicalizationBase &&) = delete;
  ~SCFForLoopCanonicalizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-canonicalization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-canonicalization"; }

  ::llvm::StringRef getDescription() const override { return "Canonicalize operations within scf.for loop bodies"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopCanonicalization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopCanonicalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
    registry.insert<tensor::TensorDialect>();
    registry.insert<memref::MemRefDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopCanonicalizationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForLoopPeelingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopPeelingBase;

  SCFForLoopPeelingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopPeelingBase(const SCFForLoopPeelingBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopPeelingBase& operator=(const SCFForLoopPeelingBase &) = delete;
  SCFForLoopPeelingBase(SCFForLoopPeelingBase &&) = delete;
  SCFForLoopPeelingBase& operator=(SCFForLoopPeelingBase &&) = delete;
  ~SCFForLoopPeelingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-peeling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-peeling"; }

  ::llvm::StringRef getDescription() const override { return "Peel `for` loops at their upper bounds."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopPeeling");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopPeeling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopPeelingBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> peelFront{*this, "peel-front", ::llvm::cl::desc("Peel the first iteration out of the loop."), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> skipPartial{*this, "skip-partial", ::llvm::cl::desc("Do not peel loops inside of the last, partial iteration of another already peeled loop."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class SCFForLoopRangeFoldingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopRangeFoldingBase;

  SCFForLoopRangeFoldingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopRangeFoldingBase(const SCFForLoopRangeFoldingBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopRangeFoldingBase& operator=(const SCFForLoopRangeFoldingBase &) = delete;
  SCFForLoopRangeFoldingBase(SCFForLoopRangeFoldingBase &&) = delete;
  SCFForLoopRangeFoldingBase& operator=(SCFForLoopRangeFoldingBase &&) = delete;
  ~SCFForLoopRangeFoldingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-range-folding");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-range-folding"; }

  ::llvm::StringRef getDescription() const override { return "Fold add/mul ops into loop range"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopRangeFolding");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopRangeFolding"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopRangeFoldingBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForLoopSpecializationBase;

  SCFForLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForLoopSpecializationBase(const SCFForLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForLoopSpecializationBase& operator=(const SCFForLoopSpecializationBase &) = delete;
  SCFForLoopSpecializationBase(SCFForLoopSpecializationBase &&) = delete;
  SCFForLoopSpecializationBase& operator=(SCFForLoopSpecializationBase &&) = delete;
  ~SCFForLoopSpecializationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize `for` loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFForLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForLoopSpecializationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForToWhileLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForToWhileLoopBase;

  SCFForToWhileLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForToWhileLoopBase(const SCFForToWhileLoopBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForToWhileLoopBase& operator=(const SCFForToWhileLoopBase &) = delete;
  SCFForToWhileLoopBase(SCFForToWhileLoopBase &&) = delete;
  SCFForToWhileLoopBase& operator=(SCFForToWhileLoopBase &&) = delete;
  ~SCFForToWhileLoopBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-for-to-while");
  }
  ::llvm::StringRef getArgument() const override { return "scf-for-to-while"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF for loops to SCF while loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForToWhileLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForToWhileLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForToWhileLoopBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForallToForLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForallToForLoopBase;

  SCFForallToForLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForallToForLoopBase(const SCFForallToForLoopBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForallToForLoopBase& operator=(const SCFForallToForLoopBase &) = delete;
  SCFForallToForLoopBase(SCFForallToForLoopBase &&) = delete;
  SCFForallToForLoopBase& operator=(SCFForallToForLoopBase &&) = delete;
  ~SCFForallToForLoopBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-forall-to-for");
  }
  ::llvm::StringRef getArgument() const override { return "scf-forall-to-for"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF forall loops to SCF for loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForallToForLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForallToForLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForallToForLoopBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFForallToParallelLoopBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFForallToParallelLoopBase;

  SCFForallToParallelLoopBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFForallToParallelLoopBase(const SCFForallToParallelLoopBase &other) : ::mlir::OperationPass<>(other) {}
  SCFForallToParallelLoopBase& operator=(const SCFForallToParallelLoopBase &) = delete;
  SCFForallToParallelLoopBase(SCFForallToParallelLoopBase &&) = delete;
  SCFForallToParallelLoopBase& operator=(SCFForallToParallelLoopBase &&) = delete;
  ~SCFForallToParallelLoopBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-forall-to-parallel");
  }
  ::llvm::StringRef getArgument() const override { return "scf-forall-to-parallel"; }

  ::llvm::StringRef getDescription() const override { return "Convert SCF forall loops to SCF parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFForallToParallelLoop");
  }
  ::llvm::StringRef getName() const override { return "SCFForallToParallelLoop"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFForallToParallelLoopBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFParallelLoopFusionBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopFusionBase;

  SCFParallelLoopFusionBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopFusionBase(const SCFParallelLoopFusionBase &other) : ::mlir::OperationPass<>(other) {}
  SCFParallelLoopFusionBase& operator=(const SCFParallelLoopFusionBase &) = delete;
  SCFParallelLoopFusionBase(SCFParallelLoopFusionBase &&) = delete;
  SCFParallelLoopFusionBase& operator=(SCFParallelLoopFusionBase &&) = delete;
  ~SCFParallelLoopFusionBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-fusion");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-fusion"; }

  ::llvm::StringRef getDescription() const override { return "Fuse adjacent parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopFusion");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopFusion"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopFusionBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFParallelLoopSpecializationBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopSpecializationBase;

  SCFParallelLoopSpecializationBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopSpecializationBase(const SCFParallelLoopSpecializationBase &other) : ::mlir::OperationPass<>(other) {}
  SCFParallelLoopSpecializationBase& operator=(const SCFParallelLoopSpecializationBase &) = delete;
  SCFParallelLoopSpecializationBase(SCFParallelLoopSpecializationBase &&) = delete;
  SCFParallelLoopSpecializationBase& operator=(SCFParallelLoopSpecializationBase &&) = delete;
  ~SCFParallelLoopSpecializationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-specialization");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-specialization"; }

  ::llvm::StringRef getDescription() const override { return "Specialize parallel loops for vectorization"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopSpecialization");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopSpecialization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopSpecializationBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SCFParallelLoopTilingBase : public ::mlir::OperationPass<> {
public:
  using Base = SCFParallelLoopTilingBase;

  SCFParallelLoopTilingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  SCFParallelLoopTilingBase(const SCFParallelLoopTilingBase &other) : ::mlir::OperationPass<>(other) {}
  SCFParallelLoopTilingBase& operator=(const SCFParallelLoopTilingBase &) = delete;
  SCFParallelLoopTilingBase(SCFParallelLoopTilingBase &&) = delete;
  SCFParallelLoopTilingBase& operator=(SCFParallelLoopTilingBase &&) = delete;
  ~SCFParallelLoopTilingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("scf-parallel-loop-tiling");
  }
  ::llvm::StringRef getArgument() const override { return "scf-parallel-loop-tiling"; }

  ::llvm::StringRef getDescription() const override { return "Tile parallel loops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SCFParallelLoopTiling");
  }
  ::llvm::StringRef getName() const override { return "SCFParallelLoopTiling"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SCFParallelLoopTilingBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<int64_t> tileSizes{*this, "parallel-loop-tile-sizes", ::llvm::cl::desc("Factors to tile parallel loops by")};
  ::mlir::Pass::Option<bool> noMinMaxBounds{*this, "no-min-max-bounds", ::llvm::cl::desc("Perform tiling with fixed upper bound with inbound check inside the internal loops"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TestSCFParallelLoopCollapsingBase : public ::mlir::OperationPass<> {
public:
  using Base = TestSCFParallelLoopCollapsingBase;

  TestSCFParallelLoopCollapsingBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  TestSCFParallelLoopCollapsingBase(const TestSCFParallelLoopCollapsingBase &other) : ::mlir::OperationPass<>(other) {}
  TestSCFParallelLoopCollapsingBase& operator=(const TestSCFParallelLoopCollapsingBase &) = delete;
  TestSCFParallelLoopCollapsingBase(TestSCFParallelLoopCollapsingBase &&) = delete;
  TestSCFParallelLoopCollapsingBase& operator=(TestSCFParallelLoopCollapsingBase &&) = delete;
  ~TestSCFParallelLoopCollapsingBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("test-scf-parallel-loop-collapsing");
  }
  ::llvm::StringRef getArgument() const override { return "test-scf-parallel-loop-collapsing"; }

  ::llvm::StringRef getDescription() const override { return "Test parallel loops collapsing transformation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TestSCFParallelLoopCollapsing");
  }
  ::llvm::StringRef getName() const override { return "TestSCFParallelLoopCollapsing"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<affine::AffineDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TestSCFParallelLoopCollapsingBase<DerivedT>)

protected:
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices0{*this, "collapsed-indices-0", ::llvm::cl::desc("Which loop indices to combine 0th loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices1{*this, "collapsed-indices-1", ::llvm::cl::desc("Which loop indices to combine into the position 1 loop index")};
  ::mlir::Pass::ListOption<unsigned> clCollapsedIndices2{*this, "collapsed-indices-2", ::llvm::cl::desc("Which loop indices to combine into the position 2 loop index")};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
