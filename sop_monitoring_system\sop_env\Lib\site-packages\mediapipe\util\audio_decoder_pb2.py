# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/util/audio_decoder.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"mediapipe/util/audio_decoder.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xc1\x01\n\x12\x41udioStreamOptions\x12\x17\n\x0cstream_index\x18\x01 \x01(\x03:\x01\x30\x12\x1c\n\rallow_missing\x18\x02 \x01(\x08:\x05\x66\x61lse\x12%\n\x16ignore_decode_failures\x18\x03 \x01(\x08:\x05\x66\x61lse\x12+\n\x1coutput_regressing_timestamps\x18\x04 \x01(\x08:\x05\x66\x61lse\x12 \n\x18\x63orrect_pts_for_rollover\x18\x05 \x01(\x08\"\xbe\x01\n\x13\x41udioDecoderOptions\x12\x33\n\x0c\x61udio_stream\x18\x01 \x03(\x0b\x32\x1d.mediapipe.AudioStreamOptions\x12\x12\n\nstart_time\x18\x02 \x01(\x01\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\x01\x32L\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xb2\xef\xca} \x01(\x0b\x32\x1e.mediapipe.AudioDecoderOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.util.audio_decoder_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_AUDIODECODEROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_AUDIOSTREAMOPTIONS']._serialized_start=88
  _globals['_AUDIOSTREAMOPTIONS']._serialized_end=281
  _globals['_AUDIODECODEROPTIONS']._serialized_start=284
  _globals['_AUDIODECODEROPTIONS']._serialized_end=474
# @@protoc_insertion_point(module_scope)
