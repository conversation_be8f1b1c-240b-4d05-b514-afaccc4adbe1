# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/tool/packet_generator_wrapper_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_options_pb2 as mediapipe_dot_framework_dot_calculator__options__pb2
from mediapipe.framework import packet_generator_pb2 as mediapipe_dot_framework_dot_packet__generator__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nBmediapipe/framework/tool/packet_generator_wrapper_calculator.proto\x12\tmediapipe\x1a,mediapipe/framework/calculator_options.proto\x1a*mediapipe/framework/packet_generator.proto\"\xeb\x01\n\'PacketGeneratorWrapperCalculatorOptions\x12\x18\n\x10packet_generator\x18\x01 \x01(\t\x12\x32\n\x07options\x18\x02 \x01(\x0b\x32!.mediapipe.PacketGeneratorOptions\x12\x0f\n\x07package\x18\x03 \x01(\t2a\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xe5\x8c\x90\xb6\x01 \x01(\x0b\x32\x32.mediapipe.PacketGeneratorWrapperCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.tool.packet_generator_wrapper_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_PACKETGENERATORWRAPPERCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_PACKETGENERATORWRAPPERCALCULATOROPTIONS']._serialized_start=172
  _globals['_PACKETGENERATORWRAPPERCALCULATOROPTIONS']._serialized_end=407
# @@protoc_insertion_point(module_scope)
