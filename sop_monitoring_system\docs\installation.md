# 安裝指南

## 系統需求

### 硬體需求
- GPU: NVIDIA GTX 1660 Ti 或更高
- RAM: 16GB 以上
- 硬碟: 10GB 可用空間

### 軟體需求
- Python 3.8+
- CUDA 11.8
- cuDNN 8.x

## 安裝步驟

### 1. 克隆專案
```bash
git clone https://github.com/your-repo/sop-monitoring-system.git
cd sop-monitoring-system
```

### 2. 建立虛擬環境
```bash
python -m venv sop_env
source sop_env/bin/activate  # Linux/Mac
# 或
sop_env\Scripts\activate  # Windows
```

### 3. 安裝依賴
```bash
pip install -r requirements.txt
```

### 4. 下載模型
```bash
cd models
python download_models.py
```

### 5. 測試安裝
```bash
python -m pytest tests/
```

### 6. 啟動監控介面
```bash
streamlit run dashboard/app.py
```

## 常見問題

### CUDA相關問題
如果遇到CUDA問題，請確保：

- 正確安裝CUDA 11.8
- 設定環境變數
- 驗證PyTorch CUDA支援

```bash
# 驗證CUDA安裝
python -c "import torch; print(torch.cuda.is_available())"
```

### 記憶體問題
如果遇到記憶體不足：
- 降低 `config.yaml` 中的 `batch_size`
- 調整 `input_size` 解析度
- 關閉其他佔用GPU記憶體的程式

### 模型下載問題
如果模型下載失敗：
- 檢查網路連線
- 手動下載模型檔案到 `models/` 資料夾
- 確認模型檔案完整性

## 效能優化建議

- **GPU加速**：確保使用NVIDIA GPU並正確安裝CUDA
- **FPS調整**：根據硬體效能調整 `fps_target`
- **ROI設定**：縮小監控區域可提升處理速度
- **批次處理**：適當調整 `batch_size` 平衡速度與記憶體使用