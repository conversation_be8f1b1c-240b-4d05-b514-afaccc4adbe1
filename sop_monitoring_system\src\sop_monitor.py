# SOP監控核心
import time
import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class SOPMonitor:
    def __init__(self, config: dict, sop_timeline: dict):
        """
        SOP監控核心類別

        Args:
            config: 系統配置
            sop_timeline: SOP時間軸定義
        """
        self.config = config
        self.sop_timeline = sop_timeline['sop_timeline']
        self.monitoring_config = config['monitoring']
        self.sop_config = config['sop']

        # 監控狀態
        self.start_time = None
        self.current_step = 0
        self.completed_steps = []
        self.alerts = []
        self.is_monitoring = False

        # 統計資料
        self.stats = {
            'total_screws': 0,
            'total_rotations': 0,
            'completion_rate': 0.0,
            'time_efficiency': 0.0,
            'alerts_count': 0
        }

    def start_monitoring(self):
        """開始監控"""
        self.start_time = time.time()
        self.current_step = 0
        self.completed_steps = []
        self.alerts = []
        self.is_monitoring = True
        self.stats = {
            'total_screws': 0,
            'total_rotations': 0,
            'completion_rate': 0.0,
            'time_efficiency': 0.0,
            'alerts_count': 0
        }
        print("🚀 SOP監控開始")

    def stop_monitoring(self):
        """停止監控"""
        self.is_monitoring = False
        total_time = time.time() - self.start_time if self.start_time else 0
        print(f"⏹️ SOP監控結束，總時間: {total_time:.2f}秒")

    def update_action(self, action_type: str, confidence: float, person_id: int = None):
        """
        更新檢測到的動作

        Args:
            action_type: 動作類型 ('screw', 'rotation', 'idle')
            confidence: 信心度
            person_id: 人員ID
        """
        if not self.is_monitoring:
            return

        current_time = time.time() - self.start_time

        # 檢查是否為目標人員
        if person_id != self.monitoring_config.get('target_person_id', 1):
            return

        # 檢查信心度閾值
        if confidence < self.monitoring_config.get('alert_threshold', 0.8):
            return

        # 檢查當前應該執行的步驟
        expected_step = self._get_expected_step(current_time)
        if expected_step:
            self._validate_action(action_type, expected_step, current_time)

    def _get_expected_step(self, current_time: float) -> Optional[dict]:
        """獲取當前時間應該執行的步驟"""
        time_tolerance = self.monitoring_config.get('time_tolerance', 2)

        for step in self.sop_timeline:
            start_time = step['start_time']
            end_time = step['end_time']

            # 考慮時間容忍度
            if (start_time - time_tolerance) <= current_time <= (end_time + time_tolerance):
                return step

        return None

    def _validate_action(self, detected_action: str, expected_step: dict, current_time: float):
        """驗證檢測到的動作是否符合預期"""
        expected_type = expected_step['type']
        step_name = expected_step['action']

        # 動作類型映射
        action_mapping = {
            'screw': 'screw',
            'rotation': 'rotation',
            'idle': None
        }

        mapped_action = action_mapping.get(detected_action)

        if mapped_action == expected_type:
            # 動作正確
            if step_name not in self.completed_steps:
                self.completed_steps.append(step_name)
                self._update_stats(expected_type)
                print(f"✅ 步驟完成: {step_name} (時間: {current_time:.1f}s)")
        else:
            # 動作不符合預期
            alert = {
                'timestamp': datetime.now(),
                'type': 'action_mismatch',
                'message': f"預期動作: {expected_type}, 檢測到: {detected_action}",
                'step': step_name,
                'time': current_time
            }
            self.alerts.append(alert)
            self.stats['alerts_count'] += 1
            print(f"⚠️ 動作不符: {alert['message']}")

    def _update_stats(self, action_type: str):
        """更新統計資料"""
        if action_type == 'screw':
            self.stats['total_screws'] += 1
        elif action_type == 'rotation':
            self.stats['total_rotations'] += 1

        # 計算完成率
        total_steps = len(self.sop_timeline)
        completed_count = len(self.completed_steps)
        self.stats['completion_rate'] = (completed_count / total_steps) * 100

        # 計算時間效率
        if self.start_time:
            elapsed_time = time.time() - self.start_time
            expected_time = self.sop_config.get('expected_duration', 52)
            if elapsed_time > 0:
                self.stats['time_efficiency'] = (expected_time / elapsed_time) * 100

    def get_current_status(self) -> dict:
        """獲取當前監控狀態"""
        if not self.is_monitoring:
            return {'status': 'stopped'}

        current_time = time.time() - self.start_time if self.start_time else 0
        expected_step = self._get_expected_step(current_time)

        return {
            'status': 'monitoring',
            'current_time': current_time,
            'expected_step': expected_step['action'] if expected_step else 'unknown',
            'completed_steps': len(self.completed_steps),
            'total_steps': len(self.sop_timeline),
            'stats': self.stats.copy(),
            'recent_alerts': self.alerts[-5:] if self.alerts else []
        }

    def get_timeline_progress(self) -> List[dict]:
        """獲取時間軸進度"""
        progress = []
        for step in self.sop_timeline:
            is_completed = step['action'] in self.completed_steps
            progress.append({
                'action': step['action'],
                'type': step['type'],
                'start_time': step['start_time'],
                'end_time': step['end_time'],
                'completed': is_completed
            })
        return progress
