# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.quantization namespace
"""

import sys as _sys

from tensorflow._api.v2.quantization import experimental
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_args # line: 2698
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_args_gradient # line: 2867
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars # line: 3003
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars_gradient # line: 3165
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars_per_channel # line: 3296
from tensorflow.python.ops.gen_array_ops import fake_quant_with_min_max_vars_per_channel_gradient # line: 3443
from tensorflow.python.ops.gen_array_ops import quantized_concat # line: 8244
from tensorflow.python.ops.array_ops import dequantize # line: 5918
from tensorflow.python.ops.array_ops import quantize # line: 5879
from tensorflow.python.ops.array_ops import quantize_and_dequantize # line: 5955
from tensorflow.python.ops.array_ops import quantize_and_dequantize_v2 # line: 6022
