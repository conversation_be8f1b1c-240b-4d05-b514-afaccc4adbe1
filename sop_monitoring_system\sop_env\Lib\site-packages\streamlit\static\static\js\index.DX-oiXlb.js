import{r as l,b2 as a,j as e,b3 as d,bn as u,b1 as c,b4 as B,b5 as b}from"./index.C1NIn1Y2.js";function m(i){const{disabled:o,element:t,widgetMgr:s,fragmentId:r}=i;let n=a.SECONDARY;return t.type==="primary"?n=a.PRIMARY:t.type==="tertiary"&&(n=a.TERTIARY),e(b,{className:"stButton","data-testid":"stButton",children:e(d,{help:t.help,containerWidth:t.useContainerWidth,children:e(u,{kind:n,size:c.SMALL,disabled:o,containerWidth:t.useContainerWidth,onClick:()=>s.setTriggerValue(t,{fromUi:!0},r),children:e(B,{icon:t.icon,label:t.label})})})})}const p=l.memo(m);export{p as default};
