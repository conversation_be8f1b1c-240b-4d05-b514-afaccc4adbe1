//===- OpenACCMPOpsInterfaces.h - MLIR Interfaces for OpenACC/MP *- C++ -*-===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//
//
// This file declares OpenACC/OpenMP Interface implementations for the
// OpenACC/OpenMP dialects.
//
//===----------------------------------------------------------------------===//

#ifndef OPENACC_MP_COMMON_INTERFACES_OPSINTERFACES_H_
#define OPENACC_MP_COMMON_INTERFACES_OPSINTERFACES_H_

#include "mlir/IR/OpDefinition.h"

#include "mlir/Dialect/OpenACCMPCommon/Interfaces/OpenACCMPOpsInterfaces.h.inc"

#endif // OPENACC_MP_COMMON_INTERFACES_OPSINTERFACES_H_
