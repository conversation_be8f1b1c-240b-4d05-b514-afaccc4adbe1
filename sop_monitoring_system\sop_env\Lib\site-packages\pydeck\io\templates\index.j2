<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <title>pydeck</title>
    {% if google_maps_key %}
    <script src="https://maps.googleapis.com/maps/api/js?key={{google_maps_key}}&libraries=places"></script>
    {% else %}
    <script src="https://api.tiles.mapbox.com/mapbox-gl-js/v1.13.0/mapbox-gl.js"></script>
    {% endif %}
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap-theme.min.css" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css" />
    {{ deckgl_jupyter_widget_bundle }}
    <style>
    {{ css_text }}
    </style>
  </head>
  <body>
    <div id="deck-container">
    </div>
  </body>
  <script>
    const container = document.getElementById('deck-container');
    const jsonInput = {{json_input}};
    const tooltip = {{tooltip}};
    const customLibraries = {{custom_libraries or 'null'}};
    const configuration = {{configuration or 'null'}};

    const deckInstance = createDeck({
      {% if mapbox_key %}
      mapboxApiKey: '{{mapbox_key}}',
      {% endif %}
      {% if google_maps_key %}
      googleMapsKey: '{{google_maps_key}}',
      {% endif %}
      container,
      jsonInput,
      tooltip,
      customLibraries,
      configuration
    });

  </script>
</html>
