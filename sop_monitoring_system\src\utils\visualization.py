# 視覺化工具
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional

class Visualizer:
    def __init__(self, config: dict):
        """
        視覺化工具

        Args:
            config: 系統配置
        """
        self.config = config
        self.monitoring_config = config['monitoring']

        # 顏色定義
        self.colors = {
            'person': (0, 255, 0),      # 綠色 - 人員
            'screw': (255, 0, 0),       # 藍色 - 螺絲
            'rotation': (0, 0, 255),    # 紅色 - 旋轉
            'roi': (255, 255, 0),       # 青色 - ROI區域
            'alert': (0, 0, 255),       # 紅色 - 警告
            'success': (0, 255, 0),     # 綠色 - 成功
            'text': (255, 255, 255),    # 白色 - 文字
            'background': (0, 0, 0)     # 黑色 - 背景
        }

        # 字體設定
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.6
        self.thickness = 2

    def draw_detections(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        繪製檢測結果

        Args:
            frame: 輸入幀
            detections: 檢測結果

        Returns:
            繪製後的幀
        """
        result_frame = frame.copy()

        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            class_name = detection.get('class_name', 'person')

            # 繪製邊界框
            x1, y1, x2, y2 = map(int, bbox)
            color = self.colors.get(class_name, self.colors['person'])
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), color, 2)

            # 繪製標籤
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, self.font, self.font_scale, self.thickness)[0]

            # 背景矩形
            cv2.rectangle(result_frame,
                         (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1),
                         color, -1)

            # 文字
            cv2.putText(result_frame, label, (x1, y1 - 5),
                       self.font, self.font_scale, self.colors['text'], self.thickness)

        return result_frame

    def draw_tracks(self, frame: np.ndarray, tracks: List[Dict]) -> np.ndarray:
        """
        繪製追踪結果

        Args:
            frame: 輸入幀
            tracks: 追踪結果

        Returns:
            繪製後的幀
        """
        result_frame = frame.copy()

        for track in tracks:
            track_id = track['track_id']
            bbox = track['bbox']
            center = track['center']

            # 繪製邊界框
            x1, y1, x2, y2 = map(int, bbox)
            color = self._get_track_color(track_id)
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), color, 2)

            # 繪製中心點
            cx, cy = map(int, center)
            cv2.circle(result_frame, (cx, cy), 5, color, -1)

            # 繪製追踪ID
            label = f"ID: {track_id}"
            cv2.putText(result_frame, label, (x1, y1 - 10),
                       self.font, self.font_scale, color, self.thickness)

        return result_frame

    def draw_pose(self, frame: np.ndarray, pose_results: List[Dict]) -> np.ndarray:
        """
        繪製姿態檢測結果

        Args:
            frame: 輸入幀
            pose_results: 姿態檢測結果

        Returns:
            繪製後的幀
        """
        result_frame = frame.copy()

        for pose in pose_results:
            keypoints = pose.get('keypoints', [])
            track_id = pose.get('track_id', 0)

            if not keypoints:
                continue

            color = self._get_track_color(track_id)

            # 繪製關鍵點
            for i, (x, y, conf) in enumerate(keypoints):
                if conf > 0.5:  # 只繪製信心度高的點
                    cv2.circle(result_frame, (int(x), int(y)), 3, color, -1)

            # 繪製骨架連接
            self._draw_skeleton(result_frame, keypoints, color)

        return result_frame

    def draw_roi(self, frame: np.ndarray) -> np.ndarray:
        """
        繪製ROI區域

        Args:
            frame: 輸入幀

        Returns:
            繪製後的幀
        """
        result_frame = frame.copy()
        roi_area = self.monitoring_config.get('roi_area', [100, 100, 500, 400])

        if roi_area:
            x1, y1, x2, y2 = roi_area
            cv2.rectangle(result_frame, (x1, y1), (x2, y2), self.colors['roi'], 2)
            cv2.putText(result_frame, "ROI", (x1, y1 - 10),
                       self.font, self.font_scale, self.colors['roi'], self.thickness)

        return result_frame

    def draw_status_info(self, frame: np.ndarray, status: Dict) -> np.ndarray:
        """
        繪製狀態資訊

        Args:
            frame: 輸入幀
            status: 狀態資訊

        Returns:
            繪製後的幀
        """
        result_frame = frame.copy()
        h, w = frame.shape[:2]

        # 狀態面板背景
        panel_height = 120
        cv2.rectangle(result_frame, (10, 10), (300, panel_height),
                     self.colors['background'], -1)
        cv2.rectangle(result_frame, (10, 10), (300, panel_height),
                     self.colors['text'], 2)

        # 狀態文字
        y_offset = 30
        line_height = 20

        status_text = [
            f"Status: {status.get('status', 'unknown')}",
            f"Time: {status.get('current_time', 0):.1f}s",
            f"Step: {status.get('expected_step', 'unknown')}",
            f"Progress: {status.get('completed_steps', 0)}/{status.get('total_steps', 0)}",
            f"Alerts: {status.get('stats', {}).get('alerts_count', 0)}"
        ]

        for i, text in enumerate(status_text):
            y_pos = y_offset + i * line_height
            cv2.putText(result_frame, text, (20, y_pos),
                       self.font, self.font_scale, self.colors['text'], 1)

        return result_frame

    def _get_track_color(self, track_id: int) -> Tuple[int, int, int]:
        """
        根據追踪ID獲取顏色

        Args:
            track_id: 追踪ID

        Returns:
            BGR顏色值
        """
        # 使用HSV色彩空間生成不同顏色
        hue = (track_id * 60) % 180
        hsv = np.uint8([[[hue, 255, 255]]])
        bgr = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
        return tuple(map(int, bgr[0, 0]))

    def _draw_skeleton(self, frame: np.ndarray, keypoints: List[Tuple], color: Tuple[int, int, int]):
        """
        繪製人體骨架

        Args:
            frame: 輸入幀
            keypoints: 關鍵點列表
            color: 顏色
        """
        # COCO 17點骨架連接定義
        skeleton = [
            [16, 14], [14, 12], [17, 15], [15, 13], [12, 13],
            [6, 12], [7, 13], [6, 7], [6, 8], [7, 9],
            [8, 10], [9, 11], [2, 3], [1, 2], [1, 3],
            [2, 4], [3, 5], [4, 6], [5, 7]
        ]

        for connection in skeleton:
            pt1_idx, pt2_idx = connection[0] - 1, connection[1] - 1  # 轉換為0-based索引

            if (pt1_idx < len(keypoints) and pt2_idx < len(keypoints) and
                keypoints[pt1_idx][2] > 0.5 and keypoints[pt2_idx][2] > 0.5):

                pt1 = (int(keypoints[pt1_idx][0]), int(keypoints[pt1_idx][1]))
                pt2 = (int(keypoints[pt2_idx][0]), int(keypoints[pt2_idx][1]))
                cv2.line(frame, pt1, pt2, color, 2)
