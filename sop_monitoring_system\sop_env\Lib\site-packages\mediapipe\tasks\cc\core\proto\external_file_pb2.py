# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/tasks/cc/core/proto/external_file.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1mediapipe/tasks/cc/core/proto/external_file.proto\x12\x1amediapipe.tasks.core.proto\"\xcd\x01\n\x0c\x45xternalFile\x12\x14\n\x0c\x66ile_content\x18\x01 \x01(\x0c\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12L\n\x14\x66ile_descriptor_meta\x18\x03 \x01(\x0b\x32..mediapipe.tasks.core.proto.FileDescriptorMeta\x12\x46\n\x11\x66ile_pointer_meta\x18\x04 \x01(\x0b\x32+.mediapipe.tasks.core.proto.FilePointerMeta\"@\n\x12\x46ileDescriptorMeta\x12\n\n\x02\x66\x64\x18\x01 \x01(\x05\x12\x0e\n\x06length\x18\x02 \x01(\x03\x12\x0e\n\x06offset\x18\x03 \x01(\x03\"2\n\x0f\x46ilePointerMeta\x12\x0f\n\x07pointer\x18\x01 \x01(\x04\x12\x0e\n\x06length\x18\x02 \x01(\x03\x42:\n%com.google.mediapipe.tasks.core.protoB\x11\x45xternalFileProto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.tasks.cc.core.proto.external_file_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n%com.google.mediapipe.tasks.core.protoB\021ExternalFileProto'
  _globals['_EXTERNALFILE']._serialized_start=82
  _globals['_EXTERNALFILE']._serialized_end=287
  _globals['_FILEDESCRIPTORMETA']._serialized_start=289
  _globals['_FILEDESCRIPTORMETA']._serialized_end=353
  _globals['_FILEPOINTERMETA']._serialized_start=355
  _globals['_FILEPOINTERMETA']._serialized_end=405
# @@protoc_insertion_point(module_scope)
