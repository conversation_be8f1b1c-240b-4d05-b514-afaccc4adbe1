# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.quantization.experimental namespace
"""

import sys as _sys

from tensorflow.compiler.mlir.quantization.tensorflow.python.quantize_model import _QuantizationComponentSpec as QuantizationComponentSpec # line: 46
from tensorflow.compiler.mlir.quantization.tensorflow.python.quantize_model import _QuantizationMethod as QuantizationMethod # line: 42
from tensorflow.compiler.mlir.quantization.tensorflow.python.quantize_model import _QuantizationOptions as QuantizationOptions # line: 38
from tensorflow.compiler.mlir.quantization.tensorflow.python.quantize_model import _UnitWiseQuantizationSpec as UnitWiseQuantizationSpec # line: 50
from tensorflow.compiler.mlir.quantization.tensorflow.python.quantize_model import quantize as quantize_saved_model # line: 798
from tensorflow.compiler.mlir.quantization.tensorflow.python.representative_dataset import TfRecordRepresentativeDatasetSaver # line: 81
