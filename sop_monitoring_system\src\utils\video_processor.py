# 影片處理工具
import cv2
import numpy as np
from typing import Generator, Tuple, Optional

class VideoProcessor:
    def __init__(self, config: dict):
        """
        影片處理器

        Args:
            config: 系統配置
        """
        self.config = config
        self.system_config = config['system']
        self.monitoring_config = config['monitoring']

        # 影片參數
        self.target_fps = self.system_config.get('fps_target', 20)
        self.input_size = tuple(self.system_config.get('input_size', [640, 480]))
        self.roi_area = self.monitoring_config.get('roi_area', [100, 100, 500, 400])

    def process_video(self, video_path: str) -> Generator[Tuple[np.ndarray, dict], None, None]:
        """
        處理影片並產生幀

        Args:
            video_path: 影片路徑

        Yields:
            Tuple[frame, metadata]: 處理後的幀和元數據
        """
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            raise ValueError(f"無法開啟影片: {video_path}")

        # 獲取影片資訊
        original_fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"📹 影片資訊: {width}x{height}, {original_fps:.1f}fps, {total_frames}幀")

        # 計算幀間隔
        frame_interval = max(1, int(original_fps / self.target_fps))
        frame_count = 0
        processed_count = 0

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 跳過幀以達到目標FPS
                if frame_count % frame_interval != 0:
                    continue

                # 處理幀
                processed_frame = self._process_frame(frame)

                # 建立元數據
                metadata = {
                    'frame_number': frame_count,
                    'processed_number': processed_count,
                    'timestamp': frame_count / original_fps,
                    'original_size': (width, height),
                    'processed_size': processed_frame.shape[:2][::-1]
                }

                yield processed_frame, metadata
                processed_count += 1

        finally:
            cap.release()
            print(f"✅ 影片處理完成: 處理了 {processed_count}/{total_frames} 幀")

    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        處理單一幀

        Args:
            frame: 原始幀

        Returns:
            處理後的幀
        """
        # 調整大小
        if frame.shape[:2][::-1] != self.input_size:
            frame = cv2.resize(frame, self.input_size)

        # 應用ROI（如果需要）
        if self.roi_area:
            x1, y1, x2, y2 = self.roi_area
            # 確保ROI在幀範圍內
            h, w = frame.shape[:2]
            x1 = max(0, min(x1, w))
            y1 = max(0, min(y1, h))
            x2 = max(x1, min(x2, w))
            y2 = max(y1, min(y2, h))

            # 在ROI區域外添加半透明遮罩
            overlay = frame.copy()
            cv2.rectangle(overlay, (0, 0), (w, h), (0, 0, 0), -1)
            cv2.rectangle(overlay, (x1, y1), (x2, y2), (255, 255, 255), -1)
            frame = cv2.addWeighted(frame, 0.7, overlay, 0.3, 0)

        return frame

    def process_webcam(self, camera_id: int = 0) -> Generator[Tuple[np.ndarray, dict], None, None]:
        """
        處理網路攝影機串流

        Args:
            camera_id: 攝影機ID

        Yields:
            Tuple[frame, metadata]: 處理後的幀和元數據
        """
        cap = cv2.VideoCapture(camera_id)

        if not cap.isOpened():
            raise ValueError(f"無法開啟攝影機: {camera_id}")

        # 設定攝影機參數
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.input_size[0])
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.input_size[1])
        cap.set(cv2.CAP_PROP_FPS, self.target_fps)

        frame_count = 0
        start_time = cv2.getTickCount()

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1
                current_time = (cv2.getTickCount() - start_time) / cv2.getTickFrequency()

                # 處理幀
                processed_frame = self._process_frame(frame)

                # 建立元數據
                metadata = {
                    'frame_number': frame_count,
                    'timestamp': current_time,
                    'is_webcam': True,
                    'processed_size': processed_frame.shape[:2][::-1]
                }

                yield processed_frame, metadata

        except KeyboardInterrupt:
            print("⏹️ 攝影機串流已停止")
        finally:
            cap.release()

    def save_frame(self, frame: np.ndarray, output_path: str):
        """
        儲存幀到檔案

        Args:
            frame: 要儲存的幀
            output_path: 輸出路徑
        """
        cv2.imwrite(output_path, frame)

    def get_roi_area(self) -> Tuple[int, int, int, int]:
        """獲取ROI區域座標"""
        return tuple(self.roi_area)
