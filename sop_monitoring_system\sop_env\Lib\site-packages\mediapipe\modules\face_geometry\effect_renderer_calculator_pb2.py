# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/modules/face_geometry/effect_renderer_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_options_pb2 as mediapipe_dot_framework_dot_calculator__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n@mediapipe/modules/face_geometry/effect_renderer_calculator.proto\x12\tmediapipe\x1a,mediapipe/framework/calculator_options.proto\"\xce\x01\n+FaceGeometryEffectRendererCalculatorOptions\x12\x1b\n\x13\x65\x66\x66\x65\x63t_texture_path\x18\x01 \x01(\t\x12\x1b\n\x13\x65\x66\x66\x65\x63t_mesh_3d_path\x18\x02 \x01(\t2e\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xf0\xd9\xac\x9a\x01 \x01(\x0b\x32\x36.mediapipe.FaceGeometryEffectRendererCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.modules.face_geometry.effect_renderer_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_FACEGEOMETRYEFFECTRENDERERCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_FACEGEOMETRYEFFECTRENDERERCALCULATOROPTIONS']._serialized_start=126
  _globals['_FACEGEOMETRYEFFECTRENDERERCALCULATOROPTIONS']._serialized_end=332
# @@protoc_insertion_point(module_scope)
