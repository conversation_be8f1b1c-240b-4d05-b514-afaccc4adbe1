# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/audio/resample_time_series_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nAmediapipe/calculators/audio/resample_time_series_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\x91\x07\n#ResampleTimeSeriesCalculatorOptions\x12\x1a\n\x12target_sample_rate\x18\x01 \x01(\x01\x12o\n\x0eresampler_type\x18\x02 \x01(\x0e\x32<.mediapipe.ResampleTimeSeriesCalculatorOptions.ResamplerType:\x19RESAMPLER_RATIONAL_FACTOR\x12q\n\x1dresampler_libresample_options\x18\x03 \x01(\x0b\x32J.mediapipe.ResampleTimeSeriesCalculatorOptions.ResamplerLibResampleOptions\x12x\n!resampler_rational_factor_options\x18\x04 \x01(\x0b\x32M.mediapipe.ResampleTimeSeriesCalculatorOptions.ResamplerRationalFactorOptions\x12+\n\x1d\x63heck_inconsistent_timestamps\x18\x05 \x01(\x08:\x04true\x12\x1e\n\x10\x61llow_upsampling\x18\x06 \x01(\x08:\x04true\x12!\n\x16min_source_sample_rate\x18\x07 \x01(\x01:\x01\x30\x1aH\n\x1bResamplerLibResampleOptions\x12)\n\x1ause_high_quality_resampler\x18\x01 \x01(\x08:\x05\x66\x61lse\x1a\x92\x01\n\x1eResamplerRationalFactorOptions\x12\x18\n\rradius_factor\x18\x04 \x01(\x01:\x01\x35\x12\x1e\n\x11\x63utoff_proportion\x18\x05 \x01(\x01:\x03\x30.9\x12\x16\n\x0bkaiser_beta\x18\x03 \x01(\x01:\x01\x36\x12\x0e\n\x06radius\x18\x01 \x01(\x01\x12\x0e\n\x06\x63utoff\x18\x02 \x01(\x01\"C\n\rResamplerType\x12\r\n\tUNDEFINED\x10\x00\x12\x1d\n\x19RESAMPLER_RATIONAL_FACTOR\x10\x02\"\x04\x08\x01\x10\x01\x32\\\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\x87\xea\xc0\x17 \x01(\x0b\x32..mediapipe.ResampleTimeSeriesCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.audio.resample_time_series_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_calculator__options__pb2.CalculatorOptions.RegisterExtension(_RESAMPLETIMESERIESCALCULATOROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS']._serialized_start=119
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS']._serialized_end=1032
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS_RESAMPLERLIBRESAMPLEOPTIONS']._serialized_start=648
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS_RESAMPLERLIBRESAMPLEOPTIONS']._serialized_end=720
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS_RESAMPLERRATIONALFACTOROPTIONS']._serialized_start=723
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS_RESAMPLERRATIONALFACTOROPTIONS']._serialized_end=869
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS_RESAMPLERTYPE']._serialized_start=871
  _globals['_RESAMPLETIMESERIESCALCULATOROPTIONS_RESAMPLERTYPE']._serialized_end=938
# @@protoc_insertion_point(module_scope)
