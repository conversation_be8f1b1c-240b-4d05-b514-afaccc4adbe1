# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/util/resource_provider_calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=mediapipe/calculators/util/resource_provider_calculator.proto\x12\tmediapipe\"\xd1\x01\n!ResourceProviderCalculatorOptions\x12\x13\n\x0bresource_id\x18\x01 \x03(\t\x12H\n\tread_mode\x18\x02 \x01(\x0e\x32\x35.mediapipe.ResourceProviderCalculatorOptions.ReadMode\"M\n\x08ReadMode\x12\x17\n\x13READ_MODE_UNDEFINED\x10\x00\x12\x14\n\x10READ_MODE_BINARY\x10\x01\x12\x12\n\x0eREAD_MODE_TEXT\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.util.resource_provider_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_RESOURCEPROVIDERCALCULATOROPTIONS']._serialized_start=77
  _globals['_RESOURCEPROVIDERCALCULATOROPTIONS']._serialized_end=286
  _globals['_RESOURCEPROVIDERCALCULATOROPTIONS_READMODE']._serialized_start=209
  _globals['_RESOURCEPROVIDERCALCULATOROPTIONS_READMODE']._serialized_end=286
# @@protoc_insertion_point(module_scope)
