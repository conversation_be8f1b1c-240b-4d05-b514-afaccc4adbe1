# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/stream_handler/fixed_size_input_stream_handler.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import mediapipe_options_pb2 as mediapipe_dot_framework_dot_mediapipe__options__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nHmediapipe/framework/stream_handler/fixed_size_input_stream_handler.proto\x12\tmediapipe\x1a+mediapipe/framework/mediapipe_options.proto\"\xdc\x01\n\"FixedSizeInputStreamHandlerOptions\x12\x1d\n\x12trigger_queue_size\x18\x01 \x01(\x05:\x01\x32\x12\x1c\n\x11target_queue_size\x18\x02 \x01(\x05:\x01\x31\x12\x1d\n\x0e\x66ixed_min_size\x18\x03 \x01(\x08:\x05\x66\x61lse2Z\n\x03\x65xt\x12\x1b.mediapipe.MediaPipeOptions\x18\xbf\xe9\xfa; \x01(\x0b\x32-.mediapipe.FixedSizeInputStreamHandlerOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.stream_handler.fixed_size_input_stream_handler_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  mediapipe_dot_framework_dot_mediapipe__options__pb2.MediaPipeOptions.RegisterExtension(_FIXEDSIZEINPUTSTREAMHANDLEROPTIONS.extensions_by_name['ext'])

  DESCRIPTOR._options = None
  _globals['_FIXEDSIZEINPUTSTREAMHANDLEROPTIONS']._serialized_start=133
  _globals['_FIXEDSIZEINPUTSTREAMHANDLEROPTIONS']._serialized_end=353
# @@protoc_insertion_point(module_scope)
