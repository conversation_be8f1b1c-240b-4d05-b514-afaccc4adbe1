# 人員追踪
import numpy as np
from typing import List, Dict, Tuple, Optional
from deep_sort_realtime.deepsort_tracker import DeepSort

class PersonTracker:
    def __init__(self, config: dict):
        """
        人員追踪器

        Args:
            config: 系統配置
        """
        self.config = config

        # 初始化 DeepSort 追踪器
        self.tracker = DeepSort(
            max_age=50,
            n_init=3,
            nms_max_overlap=1.0,
            max_cosine_distance=0.2,
            nn_budget=None,
            override_track_class=None,
            embedder="mobilenet",
            half=True,
            bgr=True,
            embedder_gpu=True,
            embedder_model_name=None,
            embedder_wts=None,
            polygon=False,
            today=None
        )

        # 追踪歷史
        self.track_history = {}
        self.active_tracks = {}

    def update(self, detections: List[Dict], frame: np.ndarray) -> List[Dict]:
        """
        更新追踪器

        Args:
            detections: 檢測結果列表
            frame: 當前幀

        Returns:
            更新後的追踪結果
        """
        if not detections:
            # 即使沒有檢測結果也要更新追踪器
            tracks = self.tracker.update_tracks([], frame=frame)
            return self._format_tracks(tracks)

        # 準備檢測數據給 DeepSort
        raw_detections = []
        for det in detections:
            bbox = det['bbox']  # [x1, y1, x2, y2]
            confidence = det['confidence']
            class_id = det.get('class_id', 0)  # 人員類別通常是0

            # DeepSort 需要 [x1, y1, w, h] 格式
            x1, y1, x2, y2 = bbox
            w = x2 - x1
            h = y2 - y1

            raw_detections.append(([x1, y1, w, h], confidence, class_id))

        # 更新追踪器
        tracks = self.tracker.update_tracks(raw_detections, frame=frame)

        # 格式化追踪結果
        formatted_tracks = self._format_tracks(tracks)

        # 更新追踪歷史
        self._update_history(formatted_tracks)

        return formatted_tracks

    def _format_tracks(self, tracks) -> List[Dict]:
        """
        格式化追踪結果

        Args:
            tracks: DeepSort 追踪結果

        Returns:
            格式化後的追踪結果
        """
        formatted_tracks = []

        for track in tracks:
            if not track.is_confirmed():
                continue

            track_id = track.track_id
            bbox = track.to_ltrb()  # [x1, y1, x2, y2]

            track_info = {
                'track_id': track_id,
                'bbox': bbox.tolist(),
                'confidence': getattr(track, 'confidence', 1.0),
                'class_id': getattr(track, 'class_id', 0),
                'age': track.age,
                'hits': track.hits,
                'center': self._get_bbox_center(bbox)
            }

            formatted_tracks.append(track_info)

        return formatted_tracks

    def _get_bbox_center(self, bbox: np.ndarray) -> Tuple[float, float]:
        """
        獲取邊界框中心點

        Args:
            bbox: 邊界框 [x1, y1, x2, y2]

        Returns:
            中心點座標 (cx, cy)
        """
        x1, y1, x2, y2 = bbox
        cx = (x1 + x2) / 2
        cy = (y1 + y2) / 2
        return (float(cx), float(cy))

    def _update_history(self, tracks: List[Dict]):
        """
        更新追踪歷史

        Args:
            tracks: 當前追踪結果
        """
        current_ids = set()

        for track in tracks:
            track_id = track['track_id']
            current_ids.add(track_id)

            # 初始化追踪歷史
            if track_id not in self.track_history:
                self.track_history[track_id] = {
                    'positions': [],
                    'timestamps': [],
                    'first_seen': len(self.track_history),
                    'last_seen': 0
                }

            # 更新位置歷史
            history = self.track_history[track_id]
            history['positions'].append(track['center'])
            history['last_seen'] = len(history['positions'])

            # 限制歷史長度
            max_history = 100
            if len(history['positions']) > max_history:
                history['positions'] = history['positions'][-max_history:]

            # 更新活躍追踪
            self.active_tracks[track_id] = track

        # 清理不活躍的追踪
        inactive_ids = set(self.active_tracks.keys()) - current_ids
        for track_id in inactive_ids:
            if track_id in self.active_tracks:
                del self.active_tracks[track_id]

    def get_track_by_id(self, track_id: int) -> Optional[Dict]:
        """
        根據ID獲取追踪資訊

        Args:
            track_id: 追踪ID

        Returns:
            追踪資訊或None
        """
        return self.active_tracks.get(track_id)

    def get_track_history(self, track_id: int) -> Optional[Dict]:
        """
        獲取追踪歷史

        Args:
            track_id: 追踪ID

        Returns:
            追踪歷史或None
        """
        return self.track_history.get(track_id)

    def get_all_active_tracks(self) -> Dict[int, Dict]:
        """獲取所有活躍追踪"""
        return self.active_tracks.copy()

    def reset(self):
        """重置追踪器"""
        self.tracker = DeepSort(
            max_age=50,
            n_init=3,
            nms_max_overlap=1.0,
            max_cosine_distance=0.2,
            nn_budget=None,
            override_track_class=None,
            embedder="mobilenet",
            half=True,
            bgr=True,
            embedder_gpu=True,
            embedder_model_name=None,
            embedder_wts=None,
            polygon=False,
            today=None
        )
        self.track_history.clear()
        self.active_tracks.clear()
