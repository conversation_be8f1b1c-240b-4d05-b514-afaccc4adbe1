# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/calculator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_options_pb2 as mediapipe_dot_framework_dot_calculator__options__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from mediapipe.framework import mediapipe_options_pb2 as mediapipe_dot_framework_dot_mediapipe__options__pb2
from mediapipe.framework import packet_factory_pb2 as mediapipe_dot_framework_dot_packet__factory__pb2
from mediapipe.framework import packet_generator_pb2 as mediapipe_dot_framework_dot_packet__generator__pb2
from mediapipe.framework import status_handler_pb2 as mediapipe_dot_framework_dot_status__handler__pb2
from mediapipe.framework import stream_handler_pb2 as mediapipe_dot_framework_dot_stream__handler__pb2

from mediapipe.framework.calculator_options_pb2 import *

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$mediapipe/framework/calculator.proto\x12\tmediapipe\x1a,mediapipe/framework/calculator_options.proto\x1a\x19google/protobuf/any.proto\x1a+mediapipe/framework/mediapipe_options.proto\x1a(mediapipe/framework/packet_factory.proto\x1a*mediapipe/framework/packet_generator.proto\x1a(mediapipe/framework/status_handler.proto\x1a(mediapipe/framework/stream_handler.proto\"Z\n\x0e\x45xecutorConfig\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12,\n\x07options\x18\x03 \x01(\x0b\x32\x1b.mediapipe.MediaPipeOptions\"\x91\x02\n\x0fInputCollection\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x18\n\x10side_packet_name\x18\x02 \x03(\t\x12\x1c\n\x13\x65xternal_input_name\x18\xea\x07 \x03(\t\x12\x38\n\ninput_type\x18\x03 \x01(\x0e\x32$.mediapipe.InputCollection.InputType\x12\x11\n\tfile_name\x18\x04 \x01(\t\"k\n\tInputType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0c\n\x08RECORDIO\x10\x01\x12\x14\n\x10\x46OREIGN_RECORDIO\x10\x02\x12\x14\n\x10\x46OREIGN_CSV_TEXT\x10\x03\x12\x17\n\x13INVALID_UPPER_BOUND\x10\x04\"J\n\x12InputCollectionSet\x12\x34\n\x10input_collection\x18\x01 \x03(\x0b\x32\x1a.mediapipe.InputCollection\"7\n\x0fInputStreamInfo\x12\x11\n\ttag_index\x18\x01 \x01(\t\x12\x11\n\tback_edge\x18\x02 \x01(\x08\"\xd1\x04\n\x0eProfilerConfig\x12$\n\x1chistogram_interval_size_usec\x18\x01 \x01(\x03\x12\x1f\n\x17num_histogram_intervals\x18\x02 \x01(\x03\x12\'\n\x1b\x65nable_input_output_latency\x18\x03 \x01(\x08\x42\x02\x18\x01\x12\x17\n\x0f\x65nable_profiler\x18\x04 \x01(\x08\x12\x1d\n\x15\x65nable_stream_latency\x18\x05 \x01(\x08\x12-\n%use_packet_timestamp_for_added_packet\x18\x06 \x01(\x08\x12\x1a\n\x12trace_log_capacity\x18\x07 \x01(\x03\x12\"\n\x1atrace_event_types_disabled\x18\x08 \x03(\x05\x12\x16\n\x0etrace_log_path\x18\t \x01(\t\x12\x17\n\x0ftrace_log_count\x18\n \x01(\x05\x12\x1f\n\x17trace_log_interval_usec\x18\x0b \x01(\x03\x12\x1d\n\x15trace_log_margin_usec\x18\x0c \x01(\x03\x12%\n\x19trace_log_duration_events\x18\r \x01(\x08\x42\x02\x18\x01\x12 \n\x18trace_log_interval_count\x18\x0e \x01(\x05\x12\x1a\n\x12trace_log_disabled\x18\x0f \x01(\x08\x12\x15\n\rtrace_enabled\x18\x10 \x01(\x08\x12 \n\x18trace_log_instant_events\x18\x11 \x01(\x08\x12\x19\n\x11\x63\x61lculator_filter\x18\x12 \x01(\t\"X\n\x16GraphRuntimeInfoConfig\x12!\n\x19\x65nable_graph_runtime_info\x18\x01 \x01(\x08\x12\x1b\n\x13\x63\x61pture_period_msec\x18\x02 \x01(\r\"\xad\x0b\n\x15\x43\x61lculatorGraphConfig\x12\x33\n\x04node\x18\x01 \x03(\x0b\x32%.mediapipe.CalculatorGraphConfig.Node\x12\x36\n\x0epacket_factory\x18\x06 \x03(\x0b\x32\x1e.mediapipe.PacketFactoryConfig\x12>\n\x10packet_generator\x18\x07 \x03(\x0b\x32 .mediapipe.PacketGeneratorConfigB\x02\x18\x01\x12\x13\n\x0bnum_threads\x18\x08 \x01(\x05\x12\x36\n\x0estatus_handler\x18\t \x03(\x0b\x32\x1e.mediapipe.StatusHandlerConfig\x12\x14\n\x0cinput_stream\x18\n \x03(\t\x12\x15\n\routput_stream\x18\x0f \x03(\t\x12\x19\n\x11input_side_packet\x18\x10 \x03(\t\x12\x1a\n\x12output_side_packet\x18\x11 \x03(\t\x12\x16\n\x0emax_queue_size\x18\x0b \x01(\x05\x12\x17\n\x0freport_deadlock\x18\x15 \x01(\x08\x12\x37\n\x0cruntime_info\x18\x16 \x01(\x0b\x32!.mediapipe.GraphRuntimeInfoConfig\x12\x41\n\x14input_stream_handler\x18\x0c \x01(\x0b\x32#.mediapipe.InputStreamHandlerConfig\x12\x43\n\x15output_stream_handler\x18\r \x01(\x0b\x32$.mediapipe.OutputStreamHandlerConfig\x12+\n\x08\x65xecutor\x18\x0e \x03(\x0b\x32\x19.mediapipe.ExecutorConfig\x12\x32\n\x0fprofiler_config\x18\x12 \x01(\x0b\x32\x19.mediapipe.ProfilerConfig\x12\x0f\n\x07package\x18\x13 \x01(\t\x12\x0c\n\x04type\x18\x14 \x01(\t\x12-\n\x07options\x18\xe9\x07 \x01(\x0b\x32\x1b.mediapipe.MediaPipeOptions\x12,\n\rgraph_options\x18\xea\x07 \x03(\x0b\x32\x14.google.protobuf.Any\x1a\xe6\x04\n\x04Node\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ncalculator\x18\x02 \x01(\t\x12\x14\n\x0cinput_stream\x18\x03 \x03(\t\x12\x15\n\routput_stream\x18\x04 \x03(\t\x12\x19\n\x11input_side_packet\x18\x05 \x03(\t\x12\x1a\n\x12output_side_packet\x18\x06 \x03(\t\x12-\n\x07options\x18\x07 \x01(\x0b\x32\x1c.mediapipe.CalculatorOptions\x12*\n\x0cnode_options\x18\x08 \x03(\x0b\x32\x14.google.protobuf.Any\x12\x14\n\x0csource_layer\x18\t \x01(\x05\x12\x18\n\x10\x62uffer_size_hint\x18\n \x01(\x05\x12\x41\n\x14input_stream_handler\x18\x0b \x01(\x0b\x32#.mediapipe.InputStreamHandlerConfig\x12\x43\n\x15output_stream_handler\x18\x0c \x01(\x0b\x32$.mediapipe.OutputStreamHandlerConfig\x12\x35\n\x11input_stream_info\x18\r \x03(\x0b\x32\x1a.mediapipe.InputStreamInfo\x12\x10\n\x08\x65xecutor\x18\x0e \x01(\t\x12\x36\n\x0fprofiler_config\x18\x0f \x01(\x0b\x32\x19.mediapipe.ProfilerConfigB\x02\x18\x01\x12\x15\n\rmax_in_flight\x18\x10 \x01(\x05\x12\x14\n\x0coption_value\x18\x11 \x03(\t\x12\x17\n\x0e\x65xternal_input\x18\xed\x07 \x03(\tB-\n\x1a\x63om.google.mediapipe.protoB\x0f\x43\x61lculatorProtoP\x00\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\032com.google.mediapipe.protoB\017CalculatorProto'
  _PROFILERCONFIG.fields_by_name['enable_input_output_latency']._options = None
  _PROFILERCONFIG.fields_by_name['enable_input_output_latency']._serialized_options = b'\030\001'
  _PROFILERCONFIG.fields_by_name['trace_log_duration_events']._options = None
  _PROFILERCONFIG.fields_by_name['trace_log_duration_events']._serialized_options = b'\030\001'
  _CALCULATORGRAPHCONFIG_NODE.fields_by_name['profiler_config']._options = None
  _CALCULATORGRAPHCONFIG_NODE.fields_by_name['profiler_config']._serialized_options = b'\030\001'
  _CALCULATORGRAPHCONFIG.fields_by_name['packet_generator']._options = None
  _CALCULATORGRAPHCONFIG.fields_by_name['packet_generator']._serialized_options = b'\030\001'
  _globals['_EXECUTORCONFIG']._serialized_start=339
  _globals['_EXECUTORCONFIG']._serialized_end=429
  _globals['_INPUTCOLLECTION']._serialized_start=432
  _globals['_INPUTCOLLECTION']._serialized_end=705
  _globals['_INPUTCOLLECTION_INPUTTYPE']._serialized_start=598
  _globals['_INPUTCOLLECTION_INPUTTYPE']._serialized_end=705
  _globals['_INPUTCOLLECTIONSET']._serialized_start=707
  _globals['_INPUTCOLLECTIONSET']._serialized_end=781
  _globals['_INPUTSTREAMINFO']._serialized_start=783
  _globals['_INPUTSTREAMINFO']._serialized_end=838
  _globals['_PROFILERCONFIG']._serialized_start=841
  _globals['_PROFILERCONFIG']._serialized_end=1434
  _globals['_GRAPHRUNTIMEINFOCONFIG']._serialized_start=1436
  _globals['_GRAPHRUNTIMEINFOCONFIG']._serialized_end=1524
  _globals['_CALCULATORGRAPHCONFIG']._serialized_start=1527
  _globals['_CALCULATORGRAPHCONFIG']._serialized_end=2980
  _globals['_CALCULATORGRAPHCONFIG_NODE']._serialized_start=2366
  _globals['_CALCULATORGRAPHCONFIG_NODE']._serialized_end=2980
# @@protoc_insertion_point(module_scope)
