# This file is MACHINE GENERATED! Do not edit.
# Generated by: tensorflow/python/tools/api/generator2/generator/generator.py script.
"""Public API for tf._api.v2.data namespace
"""

import sys as _sys

from tensorflow._api.v2.data import experimental
from tensorflow.python.data.ops.dataset_ops import AUTOTUNE # line: 103
from tensorflow.python.data.ops.dataset_ops import DatasetV2 as Dataset # line: 136
from tensorflow.python.data.ops.dataset_ops import DatasetSpec # line: 4594
from tensorflow.python.data.ops.dataset_ops import INFINITE as INFINITE_CARDINALITY # line: 113
from tensorflow.python.data.ops.dataset_ops import NumpyIterator # line: 4759
from tensorflow.python.data.ops.dataset_ops import UNKNOWN as UNKNOWN_CARDINALITY # line: 114
from tensorflow.python.data.ops.iterator_ops import IteratorBase as Iterator # line: 560
from tensorflow.python.data.ops.iterator_ops import IteratorSpec # line: 929
from tensorflow.python.data.ops.options import Options # line: 565
from tensorflow.python.data.ops.options import ThreadingOptions # line: 521
from tensorflow.python.data.ops.readers import FixedLengthRecordDatasetV2 as FixedLengthRecordDataset # line: 573
from tensorflow.python.data.ops.readers import TFRecordDatasetV2 as TFRecordDataset # line: 390
from tensorflow.python.data.ops.readers import TextLineDatasetV2 as TextLineDataset # line: 175
